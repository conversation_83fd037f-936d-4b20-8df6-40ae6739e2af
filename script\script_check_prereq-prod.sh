#!/bin/bash

CP=`cat  /apps/gitlab-runner/builds/mHDMZetj/0/idecision_source_net8/idc.decision_cb/Deployment  | grep -w "PROJECT" | head -1`

CV=`cat  /apps/gitlab-runner/builds/mHDMZetj/0/idecision_source_net8/idc.decision_cb/Deployment | grep -w "VERSION" | head -1`

CIMG=`cat  /apps/gitlab-runner/builds/mHDMZetj/0/idecision_source_net8/idc.decision_cb/Deployment | grep -w "DOCKERNIMAGES" | head -1 | sed 's/development/production/g'`

CIMGLS=`cat  /apps/gitlab-runner/builds/mHDMZetj/0/idecision_source_net8/idc.decision_cb/Deployment | grep -w "DOCKERNIMAGESLS" | head -1 | sed 's/development/production/g'`

CBUILD=`cat  /apps/gitlab-runner/builds/mHDMZetj/0/idecision_source_net8/idc.decision_cb/Deployment | grep -w "CBUILD" | head -1 | sed 's/development/production/g'`

echo "$CP" >> /apps/gitlab-runner/builds/mHDMZetj/0/idecision_source_net8/idc.decision_cb/Deployment-prod

echo "$CV" >> /apps/gitlab-runner/builds/mHDMZetj/0/idecision_source_net8/idc.decision_cb/Deployment-prod

echo "$CIMG" >> /apps/gitlab-runner/builds/mHDMZetj/0/idecision_source_net8/idc.decision_cb/Deployment-prod

echo "$CIMGLS" >> /apps/gitlab-runner/builds/mHDMZetj/0/idecision_source_net8/idc.decision_cb/Deployment-prod

echo "$CBUILD" >> /apps/gitlab-runner/builds/mHDMZetj/0/idecision_source_net8/idc.decision_cb/Deployment-prod

check=`ls -Art /apps/gitlab-runner/builds/mHDMZetj/0/idecision_source_net8/idc.decision_cb/ | grep "Deployment-prod" | tail -n 1`

if [ "$check" = "Deployment-prod" ] ; then

		source /apps/gitlab-runner/builds/mHDMZetj/0/idecision_source_net8/idc.decision_cb/Deployment-prod
		
		export $(cut -d= -f1 /apps/gitlab-runner/builds/mHDMZetj/0/idecision_source_net8/idc.decision_cb/Deployment-prod)
		
		echo "`date` || File Deployment-prod is Ready $PROJECT $VERSION $DOCKERNIMAGES" >> /apps/script/activity_deploy.log
		
	else 
		
		echo "`date` || File not Found" >> /apps/script/activity_deploy.log 

		exit 1	

fi
