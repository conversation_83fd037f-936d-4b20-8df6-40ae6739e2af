using Newtonsoft.Json;

namespace idc.decision_cb.Utilities.Models.DecisionFlow.Input;

/// <summary>
/// Represents a decision flow configuration input model.
/// </summary>
/// <example>
/// {
///     "header": {
///         "flows_id": "1",
///         "name": "Decision Flow 1",
///         "description": "This is the first decision flow."
///     },
///     "nodes": [
///         {
///             "node_id": "1",
///             "node_name": "Node 1",
///             "node_type": "start",
///             "node_next": "2"
///         },
///         {
///             "node_id": "2",
///             "node_name": "Node 2",
///             "node_type": "end",
///             "node_next": "3"
///         },
///         {
///             "node_id": "3",
///             "node_name": "Node 3",
///             "node_type": "end",
///             "node_next": null
///         }
///     ],
///     "edges": [
///         {
///             "edge_id": "1",
///             "edge_source": "1",
///             "edge_target": "2",
///             "edge_label": "Edge 1"
///         },
///         {
///             "edge_id": "2",
///             "edge_source": "2",
///             "edge_target": "3",
///             "edge_label": "Edge 2"
///         }
///     ]
/// }
/// </example>
public partial class DecisionFlowConfigurationInputModel
{
    /// <summary>
    /// Gets or sets the decision flow edges.
    /// </summary>
    [JsonProperty("edges", NullValueHandling = NullValueHandling.Ignore)]
    public List<DecisionFlowEdge>? Edges { get; set; }

    /// <summary>
    /// Gets or sets the decision flow header.
    /// </summary>
    [JsonProperty("header", NullValueHandling = NullValueHandling.Ignore)]
    public DecisionFlowHeader? Header { get; set; }

    /// <summary>
    /// Gets or sets the decision flow nodes.
    /// </summary>
    [JsonProperty("nodes", NullValueHandling = NullValueHandling.Ignore)]
    public List<DecisionFlowNode>? Nodes { get; set; }
}
