using idc.decision_cb.Utilities.Models.DecisionFlow.Output;
using idc.decision_cb.Utilities.Models.Enums;

namespace idc.decision_cb.Utilities.Models.DecisionFlow.Input;

/// <summary>
/// Represents a decision flow configuration input model.
/// </summary>
/// <example>
/// {
///     "df_header": {
///         "flows_id": "1",
///         "name": "Decision Flow 1",
///         "description": "This is the first decision flow."
///     },
///     "df_nodes": [
///         {
///             "node_id": "1",
///             "node_name": "Node 1",
///             "node_type": "start",
///             "node_next": "2"
///         },
///         {
///             "node_id": "2",
///             "node_name": "Node 2",
///             "node_type": "end",
///             "node_next": "3"
///         },
///         {
///             "node_id": "3",
///             "node_name": "Node 3",
///             "node_type": "end",
///             "node_next": null
///         }
///     ],
///     "df_edges": [
///         {
///             "edge_id": "1",
///             "edge_source": "1",
///             "edge_target": "2",
///             "edge_label": "Edge 1"
///         },
///         {
///             "edge_id": "2",
///             "edge_source": "2",
///             "edge_target": "3",
///             "edge_label": "Edge 2"
///         }
///     ]
/// }
/// </example>
public partial class DecisionFlowConfigurationInputModel
{
    /// <summary>
    /// Generates sequential steps from the decision flow edges and adds them to the steps list.
    /// </summary>
    /// <example>
    /// <code>
    /// var decisionFlowConfigurationInputModel = new DecisionFlowConfigurationInputModel();
    /// decisionFlowConfigurationInputModel.GenerateStepsAndProcess();
    /// Console.WriteLine(string.Join(Environment.NewLine, decisionFlowConfigurationInputModel.Steps));
    /// </code>
    /// </example>
    public void GenerateStepsAndProcess()
    {
        StepBuilder(stepId: 0x0, currentPos: "start");
        DistinctSteps();

        if (Steps is not null && Steps?.Count > 1)
            Steps.Add(
                item: new DecisionFlowStep
                {
                    Step = "end",
                    NoSql =
                        "THIS STEPS WILL BE IGNORED, AND USED FOR COLLECTING COMMPONENTS NAME ONLY.",
                    Components = string.Join(
                        separator: ",",
                        values: (
                            Steps
                                ?.Where(predicate: static step =>
                                    !step.Components.Equals(
                                        value: "start",
                                        comparisonType: StringComparison.OrdinalIgnoreCase
                                    )
                                )
                                .Select(selector: static step => step.Components)
                        ) ?? []
                    ),
                }
            );

        ProcessBuilder();
    }

    /// <summary>
    /// Adds the given component to the components list if it doesn't already exist in the list.
    /// </summary>
    /// <param name="components">The components list.</param>
    /// <param name="component">The component to add.</param>
    /// <example>
    /// <code>
    /// List&lt;string&gt; components = new List&lt;string&gt;();
    /// AddDistinctComponent(components, "component 1");
    /// AddDistinctComponent(components, "component 2");
    /// AddDistinctComponent(components, "component 1");
    ///
    /// // components will be ["component 1", "component 2"]
    /// </code>
    /// </example>
    private static void AddDistinctComponent(List<string> components, string component)
    {
        if (!components.Contains(component, StringComparer.CurrentCultureIgnoreCase))
            components.Add(component);
    }

    /// <summary>
    /// Adds the given step to the steps list if it doesn't already exist in the list.
    /// </summary>
    /// <param name="stepId">The step identifier.</param>
    /// <param name="components">The components of the step.</param>
    /// <example>
    /// <code>
    /// AddCommonStep(stepId: 1, components: "component 1, component 2");
    /// </code>
    /// </example>
    private void AddCommonStep(int stepId, string components) =>
        AddDistinctStep(
            step: new DecisionFlowStep
            {
                Step = $"step {stepId}",
                Components = components,
                NoSql = "",
            }
        );

    /// <summary>
    /// Adds the given step to the steps list if it doesn't already exist in the list.
    /// </summary>
    /// <param name="step">The step to add.</param>
    /// <example>
    /// <code>
    /// AddDistinctStep(new DecisionFlowStep
    /// {
    ///     Step = "step 1",
    ///     Components = "component 1, component 2",
    ///     NoSql = ""
    /// });
    /// </code>
    /// </example>
    private void AddDistinctStep(DecisionFlowStep step)
    {
        if (
            0x0
            == Steps?.Count(stepX =>
                stepX.Components.Equals(
                    value: step.Components,
                    comparisonType: StringComparison.CurrentCultureIgnoreCase
                ) && !IsSingleStepType(step.Components)
            )
        )
            Steps?.Add(item: step);
    }

    /// <summary>
    /// Checks if the given components are already existed in the decision flow steps.
    /// </summary>
    /// <param name="currentEdge">The current edge.</param>
    /// <returns>
    /// <c>true</c> if the components of the given edge are already existed in the decision flow steps; otherwise, <c>false</c>.
    /// </returns>
    /// <example>
    /// <code>
    /// bool isComponentAlreadyExisted = ComponentsIsExists(currentEdge);
    /// if (isComponentAlreadyExisted)
    /// {
    ///     // Handle existed component logic
    /// }
    /// </code>
    /// </example>
    private bool ComponentsIsExists(DecisionFlowEdge currentEdge) =>
        (
            0x0
            < Steps?.Count(predicate: step =>
                step.Components.Equals(
                    value: currentEdge.Source,
                    comparisonType: StringComparison.CurrentCultureIgnoreCase
                )
            )
        ) && IsSingleStepType(components: currentEdge.Source);

    private List<string> GatherComponents(
        ref DecisionFlowEdge? currentEdge,
        ref string currentPos,
        ref int stepId
    )
    {
        List<string> components = [];

        do
        {
            AddDistinctComponent(components: components, component: currentEdge!.Source);

            currentPos = currentEdge.Target;
            currentEdge = GetNextEdgeByCurrent(currentEdge: currentEdge);

            if (currentEdge is null)
            {
                if (IsSingleStepType(components: currentPos))
                    AddDistinctStep(
                        step: new DecisionFlowStep
                        {
                            Step = $"step {stepId++}",
                            Components = currentPos,
                            NoSql = "",
                        }
                    );
                else
                    AddDistinctComponent(components: components, component: currentPos);
                break;
            }
        } while (!IsSingleStepType(components: currentEdge.Source));

        return components;
    }

    /// <summary>
    /// Retrieves the next edge from the current edge's target in the decision flow.
    /// </summary>
    /// <param name="currentEdge">The current decision flow edge.</param>
    /// <returns>
    /// The next <see cref="DecisionFlowEdge"/> if available; otherwise, null.
    /// </returns>
    /// <example>
    /// <code>
    /// var nextEdge = GetNextEdgeByCurrent(currentEdge);
    /// if (nextEdge != null)
    /// {
    ///     // Process the next edge
    /// }
    /// </code>
    /// </example>
    private DecisionFlowEdge? GetNextEdgeByCurrent(DecisionFlowEdge currentEdge) =>
        Edges?.FirstOrDefault(predicate: edge => edge.Source == currentEdge.Target);

    /// <summary>
    /// Handles non single step type by gathering components and adding step to the steps list.
    /// </summary>
    /// <param name="stepId">The step identifier.</param>
    /// <param name="currentEdge">The current decision flow edge.</param>
    /// <param name="currentPos">The current position.</param>
    /// <example>
    /// <code>
    /// var decisionFlowConfigurationInputModel = new DecisionFlowConfigurationInputModel();
    /// decisionFlowConfigurationInputModel.HandleNonSingleStepType(stepId: 1, currentEdge, ref currentPos);
    /// Console.WriteLine(string.Join(Environment.NewLine, decisionFlowConfigurationInputModel.Steps));
    /// </code>
    /// </example>
    private void StepCreator(ref int stepId, DecisionFlowEdge? currentEdge, ref string currentPos)
    {
        if (currentEdge is null)
            return;

        if (!IsSingleStepType(components: currentEdge!.Source))
        {
            List<string> components = GatherComponents(
                currentEdge: ref currentEdge,
                currentPos: ref currentPos,
                stepId: ref stepId
            );

            SubStepCreator(
                stepId: stepId,
                currentEdge: currentEdge,
                components: string.Join(separator: ",", values: components),
                currentPos: currentPos
            );
        }
        else
        {
            DecisionComponent dc = DecisionComponent.GetByCodePrecise(currentEdge!.Source);

            if (DecisionComponent.RuleCondition == dc || DecisionComponent.ChampionChallenger == dc)
            {
                StepQueBuilder(stepId: stepId, currentEdge: currentEdge);
            }
            else if (DecisionComponent.DecisionFlow == dc)
            {
                SubStepCreator(stepId: stepId, currentEdge: currentEdge);
            }
            else
            {
                SubStepCreator(stepId: stepId, currentEdge: currentEdge);
            }
        }
    }

    /// <summary>
    /// Creates a decision flow step with the given id and adds it to the steps list.
    /// </summary>
    /// <param name="stepId">The step identifier.</param>
    /// <param name="currentEdge">The current decision flow edge.</param>
    /// <param name="components">The components of the step.</param>
    /// <param name="currentPos">The current position in the decision flow.</param>
    /// <example>
    /// <code>
    /// var decisionFlowConfigurationInputModel = new DecisionFlowConfigurationInputModel();
    /// decisionFlowConfigurationInputModel.GenerateStepsAndProcess();
    /// Console.WriteLine(string.Join(Environment.NewLine, decisionFlowConfigurationInputModel.Steps));
    /// </code>
    /// </example>
    private void SubStepCreator(
        int stepId,
        DecisionFlowEdge? currentEdge,
        string components = "",
        string currentPos = ""
    )
    {
        AddDistinctStep(
            step: new DecisionFlowStep
            {
                Step = $"step {stepId}",
                NoSql = "",
                Components = !string.IsNullOrWhiteSpace(components)
                    ? components
                    : currentEdge!.Source,
            }
        );

        if (currentEdge is not null)
            StepBuilder(
                stepId: stepId,
                currentPos: !string.IsNullOrWhiteSpace(currentPos) ? currentPos : currentEdge.Target
            );
    }

    /// <summary>
    /// Determines if the provided components string corresponds to a single step type component.
    /// </summary>
    /// <param name="components">The components string to evaluate.</param>
    /// <returns>
    /// <c>true</c> if the components represent a single step type; otherwise, <c>false</c>.
    /// </returns>
    private static bool IsSingleStepType(string components) =>
        DecisionComponent.SingleStepComponentDE.Any(predicate: x =>
            x.Code.Equals(
                value: string.Join(
                    separator: "",
                    values: components.Where(x => char.IsLetter(x) == true)
                ),
                comparisonType: StringComparison.CurrentCultureIgnoreCase
            )
        );

    /// <summary>
    /// Generates a decision flow process from the steps list.
    /// </summary>
    /// <remarks>
    /// This method iterates over the steps list and generates a decision flow process
    /// from the edges list. It will only add a process if the source edge has a single
    /// target, if the source edge has multiple targets, it will add a process for each
    /// target.
    /// </remarks>
    /// <example>
    /// <code>
    /// var decisionFlowConfigurationInputModel = new DecisionFlowConfigurationInputModel();
    /// decisionFlowConfigurationInputModel.GenerateStepsAndProcess();
    /// Console.WriteLine(string.Join(Environment.NewLine, decisionFlowConfigurationInputModel.Process));
    /// </code>
    /// </example>
    private void ProcessBuilder()
    {
        if (Steps is null || 0x0 == Steps.Count)
            return;

        Steps.ForEach(action: sourceStep =>
        {
            List<DecisionFlowEdge>? sourceEdge = Edges
                ?.Where(predicate: edge =>
                    edge.Source.Equals(
                        value: sourceStep.Components.Split(separator: ",").Last(),
                        comparisonType: StringComparison.CurrentCultureIgnoreCase
                    ) && !string.Equals(sourceStep.Step, "end", StringComparison.OrdinalIgnoreCase)
                )
                .ToList();

            if (sourceEdge is null)
                return;

            if (sourceEdge.Count > 0 && sourceEdge.Count < 2)
            {
                DecisionFlowStep? targetStep = Steps.FirstOrDefault(predicate: step =>
                    step.Components.Split(separator: ",")
                        .First()
                        .Equals(
                            value: sourceEdge.First().Target,
                            comparisonType: StringComparison.CurrentCultureIgnoreCase
                        )
                );

                if (targetStep is not null)
                    Process?.Add(
                        item: new DecisionFlowProcess
                        {
                            Source = sourceStep.Step,
                            Target = targetStep.Step,
                            Label = sourceEdge.First().Data!.Label,
                        }
                    );
            }
            else
            {
                foreach (
                    (DecisionFlowEdge edge, DecisionFlowStep targetStep) in from edge in sourceEdge
                    let targetStep = Steps.FirstOrDefault(predicate: step =>
                        step.Components.Split(separator: ",")
                            .First()
                            .Equals(
                                value: edge.Target,
                                comparisonType: StringComparison.CurrentCultureIgnoreCase
                            )
                    )
                    where targetStep is not null
                    select (edge, targetStep)
                )
                    Process?.Add(
                        item: new DecisionFlowProcess
                        {
                            Source = sourceStep.Step,
                            Target = targetStep.Step,
                            Label = edge.Data!.Label,
                        }
                    );
            }
        });
    }

    /// <summary>
    /// Builds sequential steps from a given decision flow edge and adds them to the steps list.
    /// </summary>
    /// <param name="stepId">The initial step identifier.</param>
    /// <param name="currentPos">The current position.</param>
    /// <example>
    /// <code>
    /// StepBuilder(stepId: 1, currentPos: "A");
    /// </code>
    /// </example>
    private void StepBuilder(int stepId = 0x0, string currentPos = "start")
    {
        if (Edges is null || Edges.Count == 0x0)
            return;

        DecisionFlowEdge? currentEdge = Edges?.FirstOrDefault(predicate: edge =>
            edge.Source.Equals(
                value: currentPos,
                comparisonType: StringComparison.CurrentCultureIgnoreCase
            )
        );

        if (currentEdge is null)
        {
            AddCommonStep(stepId: stepId, components: currentPos);
            return;
        }

        if (ComponentsIsExists(currentEdge: currentEdge))
            return;

        stepId++;

        if (
            currentEdge.Source.Equals(
                value: "start",
                comparisonType: StringComparison.CurrentCultureIgnoreCase
            )
        )
        {
            AddCommonStep(stepId: stepId, components: currentEdge.Source);
            StepBuilder(stepId: stepId, currentPos: currentEdge.Target);
        }
        else
        {
            StepCreator(stepId: ref stepId, currentEdge: currentEdge, currentPos: ref currentPos);
        }
    }

    /// <summary>
    /// Builds sequential steps from a given decision flow edge and adds them to the steps list.
    /// </summary>
    /// <param name="stepId">The initial step identifier.</param>
    /// <param name="currentEdge">The current edge to process.</param>
    /// <example>
    /// <code>
    /// var currentEdge = new DecisionFlowEdge { Source = "A", Target = "B", Data = new EdgeData { Label = "label1" } };
    /// StepQueBuilder(stepId: 1, currentEdge: currentEdge);
    /// </code>
    /// </example>
    private void StepQueBuilder(int stepId, DecisionFlowEdge currentEdge)
    {
        int subCounter = -0x1;
        Edges!
            .Where(edge =>
                edge.Source.Equals(
                    value: currentEdge.Source,
                    comparisonType: StringComparison.CurrentCultureIgnoreCase
                )
            )
            .ToList()
            .ForEach(edge =>
            {
                subCounter++;

                AddDistinctStep(
                    step: new DecisionFlowStep
                    {
                        Step = $"step {stepId} - {edge.Data!.Label}",
                        Components = edge.Source,
                        NoSql = "",
                    }
                );

                StepBuilder(stepId: stepId + subCounter, currentPos: edge.Target);
            });
    }
}
