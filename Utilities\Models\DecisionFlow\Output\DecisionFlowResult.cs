using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace idc.decision_cb.Utilities.Models.DecisionFlow.Output;


public class DecisionFlowResult
{
    [JsonProperty("code", NullValueHandling = NullValueHandling.Ignore)]
    public string? Code { get; set; }

    [JsonProperty("stages")]
    public List<DecisionFlowResultStage> Stages { get; set; } = [];

    [JsonProperty("timestamp", NullValueHandling = NullValueHandling.Ignore)]
    public string? Timestamp { get; set; }

    [JsonProperty("detail", NullValueHandling = NullValueHandling.Ignore)]
    public JObject? Detail { get; set; }

    [JsonProperty("parameter_results", NullValueHandling = NullValueHandling.Ignore)]
    public JObject? Parameters { get; set; }

    [JsonProperty("temp_results", NullValueHandling = NullValueHandling.Ignore)]
    public JObject? TempResults { get; set; }
}
