using System.ComponentModel;

namespace idc.decision_cb.Utilities.Extensions;

internal static class EnumExtensions
{
    /// <summary>
    /// Membaca nama enum berdasarkan nilai yang diberikan.
    /// </summary>
    /// <param name="value"><PERSON><PERSON> enum yang akan dibaca.</param>
    /// <returns>Nama enum yang sesuai dengan nilai yang diberikan.</returns>
    internal static string? GetNameFromValue(this Enum value)
    {
        string name = value.ToString();
        System.Reflection.FieldInfo? field = value.GetType().GetField(name);
        if (field != null)
        {
            object[] attributes = field.GetCustomAttributes(typeof(DescriptionAttribute), false);
            if (attributes.Length > 0)
            {
                DescriptionAttribute description = (DescriptionAttribute)attributes[0];
                return description.Description;
            }
        }
        return null;
    }
}
