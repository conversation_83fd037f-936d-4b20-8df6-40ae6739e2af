using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;

namespace idc.decision_cb.Utilities.Models.DecisionFlow.Output;

/// <summary>
/// Represents a decision flow process.
/// </summary>
public class DecisionFlowProcess
{
    /// <summary>
    /// Gets or sets the label of the process, used for display purposes only.
    /// </summary>
    [JsonProperty("label", NullValueHandling = NullValueHandling.Ignore)]
    public string? Label { get; set; }

    /// <summary>
    /// Gets or sets the source of the process.
    /// </summary>
    [JsonProperty("source")]
    [Required(ErrorMessage = "DfProcess: source is required.")]
    public required string Source { get; set; }

    /// <summary>
    /// Gets or sets the target of the process.
    /// </summary>
    [JsonProperty("target")]
    [Required(ErrorMessage = "DfProcess: target is required.")]
    public required string Target { get; set; }
}
