#!/bin/bash

export TERM=xterm

source /apps/gitlab-runner/builds/mHDMZetj/0/idecision_source_net8/idc.decision_cb/Deployment

export $(cut -d= -f1 /apps/gitlab-runner/builds/mHDMZetj/0/idecision_source_net8/idc.decision_cb/Deployment)

cd /apps/script/deploy/ && git clone -b release_dev ********************:idecision_source_net8/"$PROJECT".git

sudo tar -czvf  /apps/script/archive/"$PROJECT"-$(date +%Y%m%d_%H%M).tar.gz /apps/script/deploy/"$PROJECT" --remove

check_archive=`ls -alrth /apps/script/archive/ | grep "$PROJECT" | awk '{print $9}' | awk -F '-' '{print $1}' | tail -1`

if [ "$check_archive" = "$PROJECT" ] ; then

		echo "`date` || Archive $PROJECT succesfully" >> /apps/script/activity_deploy.log

	else

		echo "`date` || Archive $PROJECT is failed" >> /apps/script/activity_deploy.log
		
fi
