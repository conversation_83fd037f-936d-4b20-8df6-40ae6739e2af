using IDX.Utilities.DataProcessor;

namespace idc.decision_cb.Utilities.Middlewares;

/// <summary>
/// Represents a multiple instance of SqLiteHelper.
/// </summary>
/// <example>
/// <code>
/// var inMemory = new SqLiteHelper("Data Source=:memory:");
/// var file = new SqLiteHelper("Data Source=wwwroot/my-sqlite.db");
/// using var multipleSqlite = new MultipleSqliteInstance(inMemory, file);
/// var result = multipleSqlite.InMemory.ExecuteScalar("SELECT 1 + 1");
/// Console.WriteLine(result); // 2
/// </code>
/// </example>
public class MultipleSqliteInstance(SqLiteHelper inMemory, SqLiteHelper file) : IDisposable
{
    private bool disposedValue;

    /// <summary>
    /// Gets or sets the SqLiteHelper of in-memory.
    /// </summary>
    public SqLiteHelper InMemory { get; set; } = inMemory;

    /// <summary>
    /// Gets or sets the SqLiteHelper of file.
    /// </summary>
    public SqLiteHelper File { get; set; } = file;

    /// <summary>
    /// Releases all resources used by the <see cref="MultipleSqliteInstance"/>.
    /// </summary>
    public void Dispose()
    {
        Dispose(disposing: true);
        GC.SuppressFinalize(this);
    }

    /// <summary>
    /// Releases unmanaged and - optionally - managed resources.
    /// </summary>
    /// <param name="disposing"><c>true</c> to release both managed and unmanaged resources; <c>false</c> to release only unmanaged resources.</param>
    protected virtual void Dispose(bool disposing)
    {
        if (!disposedValue)
        {
            if (disposing)
            {
                InMemory.Disconnect().Dispose();
                File.Disconnect().Dispose();
            }

            disposedValue = true;
        }
    }

    /// <summary>
    /// Releases unmanaged resources and performs other cleanup operations before the
    /// <see cref="MultipleSqliteInstance"/> is reclaimed by garbage collection.
    /// </summary>
    ~MultipleSqliteInstance()
    {
        // Do not change this code. Put cleanup code in 'Dispose(bool disposing)' method
        Dispose(disposing: false);
    }
}
