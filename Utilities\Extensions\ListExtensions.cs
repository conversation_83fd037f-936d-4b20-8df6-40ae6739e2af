﻿using Newtonsoft.Json.Linq;

namespace idc.decision_cb.Utilities.Extensions;

public static class ListExtensions
{
    /// <summary>
    /// Adds a value to a list of strings, ensuring it doesn't already exist.
    /// </summary>
    /// <param name="list">The list of strings to add to.</param>
    /// <param name="value">The string value to add.</param>
    /// <returns>The list with the added value if it didn't already exist, otherwise the original list.</returns>
    /// <example>
    /// <code>
    /// List&lt;string&gt; list = new() { "one", "two", "three" };
    /// List&lt;string&gt; result = list.AddDistinct("four");
    /// // result is now { "one", "two", "three", "four" }
    /// </code>
    /// </example>
    internal static List<string>? AddDistinct(this List<string>? list, string value)
    {
        if (list == null)
            return [value];

        return (List<string>?)(list.Contains(value) ? list : [.. list, value]);
    }

    /// <summary>
    /// Converts a list of dynamic objects to a <see cref="JObject"/>. Each item in the list is converted to a <see cref="JObject"/> and added to the root object with its index as the key.
    /// </summary>
    /// <param name="list">The list of dynamic objects to convert.</param>
    /// <returns>A <see cref="JObject"/> containing the converted list items.</returns>
    /// <example>
    /// <code>
    /// List&lt;dynamic&gt; list = new()
    /// {
    ///     new { Key1 = "Value1", Key2 = 1 },
    ///     new { Key3 = "Value3", Key4 = true }
    /// };
    ///
    /// JObject jObject = list.ToJObject();
    /// // jObject now contains a JObject with the items:
    /// // {
    /// //     "0": { "Key1": "Value1", "Key2": 1 },
    /// //     "1": { "Key3": "Value3", "Key4": true }
    /// // }
    /// </code>
    /// </example>
    internal static JObject? ToJObject(this List<dynamic>? list)
    {
        if (list == null)
            return null;

        JObject jObject = [];

        for (int i = 0; i < list.Count; i++)
        {
            dynamic item = list[i];
            dynamic itemJObject = JObject.FromObject(item);

            jObject[i.ToString()] = itemJObject;
        }

        return jObject;
    }
}
