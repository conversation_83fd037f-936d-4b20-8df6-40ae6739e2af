using Newtonsoft.Json;

namespace idc.decision_cb.Utilities.Models.ScoreCard.Result;

public class ScorecardCharacteristicsResultModel
{
    [JsonProperty("scr_rsh_id")]
    public int RshId { get; set; } = 0;

    [JsonProperty("scr_characteristic_name")]
    public required string Name { get; set; }

    [JsonProperty("scr_scc")]
    public required string SccName { get; set; }

    [JsonProperty("scr_score")]
    public required object? Score { get; set; }

    [JsonProperty("scr_cust_value")]
    public required object? CustValue { get; set; }

}