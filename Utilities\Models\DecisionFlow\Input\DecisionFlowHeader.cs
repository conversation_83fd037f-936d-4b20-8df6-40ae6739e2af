using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;

namespace idc.decision_cb.Utilities.Models.DecisionFlow.Input;

/// <summary>
/// Represents a decision flow header.
/// </summary>
/// <example>
/// {
///     "flows_id": 1,
///     "name": "Decision Flow 1"
/// }
/// </example>
public class DecisionFlowHeader
{
    /// <summary>
    /// Gets or sets the decision flow id.
    /// </summary>
    /// <value>The decision flow id.</value>
    [
        JsonProperty("flows_id"),
        Required(ErrorMessage = "DecisionFlowHeader: flows_id is required.")
    ]
    public required long FlowsId { get; set; }

    /// <summary>
    /// Gets or sets the decision flow name.
    /// </summary>
    /// <value>The decision flow name.</value>
    [JsonProperty("name"), Required(ErrorMessage = "DecisionFlowHeader: name is required.")]
    public required string Name { get; set; }
}
