{"comment": "add docstring to the code using xml format, including exception informations if any. Don't make any changes to the code!!!", "ApplicationName": "IDC.Alert", "Language": "EN", "Logging": {"LogLevel": {"Default": "Information"}, "Config": {"WriteToSystem": true, "FilePath": "C:\\Log.txt"}}, "Cors": {"Enabled": true, "AllowedHosts": ["http://*", "https://*", "http://localhost:*"], "AllowedHeaders": [], "AllowedMethods": ["GET", "POST"]}, "Security": {"EncryptionKey": "idxpartners", "EncryptionSalts": [73, 118, 97, 110, 32, 77, 101, 100, 118, 101, 100, 101, 118], "DBPassword": "3wwzDrxLd/RUB+gmomT6OtZW7tYiGZISsfRgFIWNXAo="}, "Swagger": {"Version": "V1", "Title": "IDX.Boilerplate API Documentationa", "Description": "A simple example for swagger api information", "TermsOfService": "https://example.com/terms", "Contact": {"Name": "Your Name XYZ", "Email": "<EMAIL>", "Url": "https://example.com"}, "License": {"Name": "Use under OpenApiLicense", "Url": "https://example.com/license"}, "Theme": "flattop"}, "Cache": {"AbsoluteExpiration": 100, "SlidingExpiration": 100}, "Secret": {"Provider": "GSM", "switcher": false, "switcher_config": true, "timeOut": 5000, "urlAPI_idcconfig": "http://localhost:32029/idcconfig/"}, "ConnectionStrings": {"local": "User ID=postgres;Password=******;HOST=localhost;Port=5445;Database=Boilerplate;Integrated Security=true;Pooling=true;MinPoolSize=1;MaxPoolSize=1000;", "idc_core": "User ID=idc_fadhly;Password=******;HOST=localhost;Port=5432;Database=idc.core;Integrated Security=true;Pooling=true;MinPoolSize=1;MaxPoolSize=1000;", "idc_kaml": "User ID=idc_fadhly;Password=******;HOST=localhost;Port=5432;Database=idc.kaml;Integrated Security=true;Pooling=true;MinPoolSize=1;MaxPoolSize=1000;"}}