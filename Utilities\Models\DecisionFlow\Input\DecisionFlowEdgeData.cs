using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;

namespace idc.decision_cb.Utilities.Models.DecisionFlow.Input;

/// <summary>
/// Represents a decision flow edge data.
/// </summary>
public class DecisionFlowEdgeData
{
    /// <summary>
    /// Gets or sets the edge identifier.
    /// </summary>
    /// <value>The edge identifier.</value>
    [JsonProperty("id"), Required(ErrorMessage = "DecisionFlowEdgeData: id is required.")]
    public required string Id { get; set; }

    /// <summary>
    /// Gets or sets the edge label.
    /// </summary>
    /// <value>The edge label.</value>
    [JsonProperty("label"), Required(ErrorMessage = "DecisionFlowEdgeData: label is required.")]
    public required string Label { get; set; }

    /// <summary>
    /// Gets or sets the edge type.
    /// </summary>
    /// <value>The edge type.</value>
    [JsonProperty("type"), Required(ErrorMessage = "DecisionFlowEdgeData: type is required.")]
    public required string Type { get; set; }
}
