using idc.decision_cb.Utilities;
using idc.decision_cb.Utilities.Constants;
using idc.decision_cb.Utilities.Extensions;
using IDX.Utilities;
using IDX.Utilities.DataProcessor;
using IDX.Utilities.Models.API;
using IDX.Utilities.Validations;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;
using NpgsqlTypes;

namespace idc.decision_cb.Controllers;

[Route("api/[controller]")]
[ApiController]
public class ComponentSyncController(SystemLogging logging, JToken language, PgSqlHelper pgHelper)
    : ControllerBase
{
    [HttpPost("Components/{componentId}")]
    public async Task<ApiResponseData<JObject?>> SyncComponents(
        string? componentId,
        CancellationToken cancellationToken = default
    )
    {
        ApiResponseData<JObject?> result = new();

        try
        {
            componentId.EnsureNotNullOrWhiteSpace(nameof(componentId));

            await pgHelper.ConnectAsync(
                useTransaction: false,
                disconnectFirst: false,
                cancellationToken: cancellationToken
            );
            await pgHelper.ExecuteScalarAsync<string?>(
                cancellationToken: cancellationToken,
                spCallInfo: new()
                {
                    Schema = "inmemory_de",
                    SPName = "df_sync_status_upsert_componet_queue",
                    Parameters =
                    [
                        new()
                        {
                            Name = "p_comp_code",
                            Value = componentId,
                            DataType = NpgsqlDbType.Text,
                        },
                    ],
                },
                callback: async output =>
                    await Task.Run(
                        () =>
                            result
                                .ChangeStatus(
                                    languageToken: language,
                                    propertyName: GeneralConstant.API_RESP_REQ_SUCCESS
                                )
                                .ChangeMessage(
                                    languageToken: language,
                                    propertyName: GeneralConstant.API_RESP_QUE_BUILDER_SUCCESS
                                )
                                .ChangeData(data: output.ToJObject())
                    )
            );
        }
        catch (Exception ex)
        {
            result
                .ChangeStatus(
                    languageToken: language,
                    propertyName: GeneralConstant.API_RESP_FAILED
                )
                .ChangeMessage(
                    exception: ex,
                    logging: logging,
                    includeStackTrace: Commons.IsDevelopmentMode()
                );
        }

        return result;
    }

    [HttpPost("DecisionFlows/{decisionFlowId}")]
    public async Task<ApiResponseData<JArray?>> SyncDecisionFlows(
        string? decisionFlowId,
        CancellationToken cancellationToken = default
    )
    {
        ApiResponseData<JArray?> result = new();

        try
        {
            decisionFlowId.EnsureNotNullOrWhiteSpace(nameof(decisionFlowId));

            await pgHelper.ConnectAsync(
                useTransaction: false,
                disconnectFirst: false,
                cancellationToken: cancellationToken
            );
            await pgHelper.ExecuteScalarAsync<string?>(
                cancellationToken: cancellationToken,
                spCallInfo: new()
                {
                    Schema = "inmemory_de",
                    SPName = "df_sync_status_flag_df_as_synced",
                    Parameters =
                    [
                        new()
                        {
                            Name = "p_df_code",
                            Value = decisionFlowId,
                            DataType = NpgsqlDbType.Text,
                        },
                    ],
                },
                callback: async output =>
                    await Task.Run(
                        () =>
                            result
                                .ChangeStatus(
                                    languageToken: language,
                                    propertyName: GeneralConstant.API_RESP_REQ_SUCCESS
                                )
                                .ChangeMessage(
                                    languageToken: language,
                                    propertyName: GeneralConstant.API_RESP_QUE_BUILDER_SUCCESS
                                )
                                .ChangeData(data: output.ToJArray())
                    )
            );
        }
        catch (Exception ex)
        {
            result
                .ChangeStatus(
                    languageToken: language,
                    propertyName: GeneralConstant.API_RESP_FAILED
                )
                .ChangeMessage(
                    exception: ex,
                    logging: logging,
                    includeStackTrace: Commons.IsDevelopmentMode()
                );
        }

        return result;
    }
}
