using System.Data;
using System.Text.RegularExpressions;
using idc.decision_cb.Utilities.Constants;
using idc.decision_cb.Utilities.Models.DecisionFlow.Output;
using idc.decision_cb.Utilities.Models.Enums;
using IDX.Utilities;
using IDX.Utilities.DataProcessor;
using Newtonsoft.Json.Linq;
using SqlKata.Compilers;

namespace idc.decision_cb.Utilities.Models.DecisionFlow.Input;

public partial class DecisionFlowConfigurationInputModel
{
    [GeneratedRegex(@"\s{2,}")]
    private static partial Regex StringWithMultipleSpaces();

    private void QueryBuilder()
    {
        if (Queries is null || Queries.Count == 0x0)
            return;

        Steps
            ?.Where(predicate: step =>
                !step.Components.Equals(
                    value: "start",
                    comparisonType: StringComparison.OrdinalIgnoreCase
                )
                && !step.Step.Equals(
                    value: "end",
                    comparisonType: StringComparison.OrdinalIgnoreCase
                )
            )
            .ToList()
            .ForEach(action: step =>
            {
                List<string> listQuery = [];
                string[] components = step!.Components.Split(separator: ",");

                components
                    .ToList()
                    .ForEach(action: component =>
                    {
                        DecisionComponent dc = DecisionComponent.GetByCodePrecise(component);

                        if (dc == DecisionComponent.RuleSet)
                            RuleSetStepPicker(component, dc, ref listQuery);
                        else if (dc != DecisionComponent.DecisionFlow)
                            SrcFieldsPicker(component, dc, ref listQuery);
                    });

                if (components.Length > 0x1)
                {
                    List<JProperty> keyValues =
                    [
                        .. listQuery.Select(x => new JProperty(
                            name: x[..x.IndexOf(value: '=')],
                            content: x[(x.IndexOf(value: '=') + 1)..]
                        )),
                    ];

                    for (int i = 1; i < keyValues.Count; i++)
                        keyValues[i].Value = Regex.Replace(
                            input: keyValues[i].Value.ToString(),
                            pattern: @$"(?<![a-zA-Z0-9_]){keyValues[i - 1].Name.Replace(oldValue: " ", newValue: "")}(?![a-zA-Z0-9_])",
                            replacement: keyValues[i - 1].Value.ToString()
                        );

                    listQuery = [.. keyValues.Select(selector: x => $"{x.Name} = {x.Value}")];
                }

                step.NoSql = GenerateUpdateQuery(fieldSet: [.. listQuery]);

                ScorecardCharacteristicsBuilder(step: step);
            });
    }

    private void ScorecardCharacteristicsBuilder(DecisionFlowStep step)
    {
        JObject joSccChars = [];
        step!
            .Components.Split(separator: ",")
            .ToList()
            .ForEach(action: component =>
            {
                DecisionComponent dc = DecisionComponent.GetByCodePrecise(code: component);

                if (dc == DecisionComponent.ScoreCard)
                    joSccChars[component] = Queries
                        ?.FirstOrDefault(x =>
                            x?.GetValue<string>(propertyName: dc.KeyName) == component
                        )
                        ?.GetValue<JObject>(propertyName: "scc_chars");
                else if (dc == DecisionComponent.RuleSet)
                    joSccChars =
                        Queries
                            ?.FirstOrDefault(x =>
                                x?.GetValue<string>(propertyName: dc.KeyName) == component
                            )
                            ?.GetValue<JArray>(propertyName: "ruleset_steps")
                            ?[0]?.GetValue<JObject>(propertyName: "scc_chars") ?? [];
            });
        step.ScorecardCharacteristics = joSccChars;
    }

    private void RuleSetStepPicker(
        string component,
        DecisionComponent dc,
        ref List<string> listQuery
    )
    {
        JArray? rulesetSteps =
            Queries
                ?.FirstOrDefault(predicate: y =>
                    y?.GetValue(propertyName: dc.KeyName)?.ToString() == component
                )
                ?.GetValue<JArray>(propertyName: "ruleset_steps") ?? [];

        if (rulesetSteps is null && rulesetSteps?.Count == 0x0)
            return;

        JArray? srcFields =
            rulesetSteps?.FirstOrDefault()?.GetValue<JArray>(propertyName: "src_fields") ?? [];

        if (srcFields is null && srcFields?.Count == 0x0)
            return;

        listQuery.AddRange(collection: srcFields?.ToObject<List<string>>() ?? []);
    }

    private void SrcFieldsPicker(string component, DecisionComponent dc, ref List<string> listQuery)
    {
        JArray sourceFields = JArray.Parse(
            json: Queries
                ?.FirstOrDefault(predicate: y =>
                    y?.GetValue(propertyName: dc.KeyName)?.ToString() == component
                )
                ?.GetValue(propertyName: "src_fields")
                ?.ToString()
                ?.Trim(trimChar: ' ') ?? "[]"
        );

        listQuery.AddRange(collection: sourceFields.ToObject<List<string>>() ?? []);
    }

    private static string GenerateUpdateQuery(string[] fieldSet)
    {
        if (fieldSet is null || fieldSet.Length == 0x0)
            return "";

        List<string> retVal =
        [
            .. fieldSet
                .Where(predicate: static x => x != null)
                .Select(selector: static x => x.ToString()[..x.ToString().IndexOf('=')].Trim()),
        ];

        string sQuery = GeneralConstant.DB_DF_QUERY_GENERATOR.BindWith(
            replacement: new Dictionary<string, object>()
            {
                { "{{field_set}}", string.Join(",", fieldSet) },
                { "{{return_value}}", string.Join(",", retVal) },
                { "\n", "" },
                { "\r", "" },
                { "\t", "" },
            }
        );

        return StringWithMultipleSpaces().Replace(input: sQuery, replacement: " ").Trim();
    }

    private void QueryCollector(PgSqlHelper pgHelper)
    {
        if (Steps is null || Steps.Count == 0x0)
            return;

        string? components = Steps
            ?.FirstOrDefault(predicate: step =>
                step.Step.Equals(value: "end", comparisonType: StringComparison.OrdinalIgnoreCase)
            )
            ?.Components;

        if (components is null)
            return;

        SqlKata.Query query = new SqlKata.Query("public.component_de_cb AS cdc").Select(
            columns: "data_dtl"
        );

        components
            .Split(separator: ",")
            .Distinct()
            .ToList()
            .ForEach(action: component =>
                query.OrWhereRaw(
                    sql: $"cdc.data_dtl->>'{DecisionComponent.GetByCodePrecise(code: component).KeyName}' = '{component}'"
                )
            );

        pgHelper
            .Connect()
            .ExecuteQuery(
                result: out List<dynamic>? result,
                query: new PostgresCompiler().Compile(query: query).ToString()
            );

        if (result is null || result.Count == 0)
            return;

        List<JObject> parsedResult = [];
        result.ForEach(action: item =>
        {
            if (item is null)
                return;

            dynamic joItem = JObject.FromObject(item);
            if (joItem != null)
                parsedResult.Add(item: JObject.Parse(json: joItem.GetValue("data_dtl").ToString()));
        });

        Queries = parsedResult;
        QueryBuilder();
    }
}
