{"IndividualSettings": {"idc.DecisionCB": {"ApplicationName": "IDC-Decision New Generation", "Language": "EN", "DI": {"httpClient": true, "memCache": true, "pgsql": true, "couchbase": false, "sqlite": true, "pgsql_default_constring": "ConnectionString_en", "couchbase_default_constring": "fcm", "sqlite_default_constring": "de_configs"}, "Logging": {"LogLevel": {"Default": "Information"}, "Config": {"WriteToSystem": false, "ForceWriteToFile": true, "FilePath": null}}, "Cors": {"Enabled": true, "AllowedHosts": ["http://*", "https://*", "http://localhost:*"], "AllowedHeaders": [], "AllowedMethods": ["GET", "POST"]}, "Swagger": {"UI": {"Enabled": true, "SortEndpoints": true, "Theme": "monokai-dark"}, "OpenAPIInfo": {"Version": "V1", "Title": "API Documentations", "Description": "A simple example for swagger api information"}}, "ViewJsonURL": "http://***********:32057/addValueData"}}, "configPass": {"passwordDB": "3wwzDrxLd/RUB+gmomT6OtZW7tYiGZISsfRgFIWNXAo="}, "KeyConvert": {"EncryptionKey": "idxpartners", "DecryptionKey": "idxpartners", "EncryptionSalts": [73, 118, 97, 110, 32, 77, 101, 100, 118, 101, 100, 101, 118]}, "Swagger": {"OpenAPIInfo": {"TermsOfService": "https://example.com/terms", "Contact": {"Name": "Your Name XYZ", "Email": "<EMAIL>", "Url": "https://example.com"}, "License": {"Name": "Use under OpenApiLicense", "Url": "https://example.com/license"}}}, "Secret": {"Provider": "GSM", "switcher": false, "switcher_config": false, "timeOut": 5000, "InMemoryCacheConfigurations": {"AbsoluteExpiration": 100, "SlidingExpiration": 100}, "urlAPI_idcconfig": "http://localhost:32029/idcconfig/"}, "CBContextSettings": {"fcm": {"host": "***************", "port": 8091, "username": "Administrator", "password": "rU9xTI7p6uCMQKIb4Hdftpx8yXxLIXAzmvLj7FHERs4=", "basicAuth": "rU9xTI7p6uCMQKIb4Hdftpx8yXxLIXAzmvLj7FHERs4="}}, "DbContextSettings": {"ConnectionString_en": "User ID=idc_ali;Password=******;HOST=localhost;Port=5431;Database=idc.en;Integrated Security=true;Pooling=true;MinPoolSize=1;MaxPoolSize=1000;"}, "SqLiteContextSettings": {"memory": "Data Source=:memory:;Cache=Private;Mode=Memory", "de_configs": "Data Source=/Users/<USER>/Documents/IDX/Repo/net8/idc.decision_cb/files/sqlite.db;Cache=Shared;Mode=ReadWrite"}}