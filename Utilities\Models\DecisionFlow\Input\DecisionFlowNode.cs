using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;

namespace idc.decision_cb.Utilities.Models.DecisionFlow.Input;

/// <summary>
/// Represents a decision flow node.
/// </summary>
public class DecisionFlowNode
{
    /// <summary>
    /// Gets or sets the node identifier.
    /// </summary>
    [JsonProperty("id"), Required(ErrorMessage = "DecisionFlowNode: id is required.")]
    public required string Id { get; set; }

    /// <summary>
    /// Gets or sets the node text.
    /// </summary>
    [JsonProperty("text"), Required(ErrorMessage = "DecisionFlowNode: text is required.")]
    public required string Text { get; set; }

    /// <summary>
    /// Gets or sets the node output.
    /// </summary>
    [JsonProperty(propertyName: "output", NullValueHandling = NullValueHandling.Ignore)]
    public string? Output { get; set; }

    /// <summary>
    /// Gets or sets the node NoSql data.
    /// </summary>
    [JsonProperty(propertyName: "no_sql", NullValueHandling = NullValueHandling.Ignore)]
    public string? NoSql { get; set; }

    /// <summary>
    /// Gets or sets the time period associated with the node.
    /// </summary>
    [JsonProperty(propertyName: "time_period", NullValueHandling = NullValueHandling.Ignore)]
    public string? TimePeriod { get; set; }
}
