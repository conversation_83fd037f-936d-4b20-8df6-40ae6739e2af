using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;

namespace idc.decision_cb.Utilities.Models.ScoreCard.Input;

/// <summary>
/// Represents a characteristic of a score card.
/// </summary>
/// <example>
/// {
///     "char_id": "1",
///     "char_field": "field_1",
///     "char_type": "integer",
///     "char_default": "0",
///     "char_koef": "1.0",
///     "detail": [
///         {
///             "limit": "18",
///             "score": "0",
///             "baseline": "true"
///         }
///     ]
/// }
/// </example>
public class ScoreCardCharacteristicModel
{
    /// <summary>
    /// Default value of the characteristic.
    /// </summary>
    [JsonProperty("char_default")]
    [Required(ErrorMessage = "ScoreCardCharacteristicModel: Default is required.")]
    public required string Default { get; set; }

    /// <summary>
    /// Details of the characteristic.
    /// </summary>
    [JsonProperty("detail")]
    [Required(ErrorMessage = "ScoreCardCharacteristicModel: Detail is required.")]
    public required List<ScoreCardCharacteristicDetailModel> Detail { get; set; }

    /// <summary>
    /// Field name of the characteristic.
    /// </summary>
    [JsonProperty("char_field")]
    [Required(ErrorMessage = "ScoreCardCharacteristicModel: Field is required.")]
    public required string Field { get; set; }

    /// <summary>
    /// Id of the characteristic.
    /// </summary>
    [JsonProperty("char_id")]
    [Required(ErrorMessage = "ScoreCardCharacteristicModel: Id is required.")]
    public required string Id { get; set; }

    /// <summary>
    /// Coefficient of the characteristic.
    /// </summary>
    [JsonProperty("char_koef")]
    [Required(ErrorMessage = "ScoreCardCharacteristicModel: Koef is required.")]
    public required string Koef { get; set; }

    /// <summary>
    /// Type of the characteristic.
    /// </summary>
    [JsonProperty("char_type")]
    [Required(ErrorMessage = "ScoreCardCharacteristicModel: Type is required.")]
    public required string Type { get; set; }
}
