﻿﻿using System.Data;

namespace idc.decision_cb.Utilities.Models.Enums;

public class DecisionComponent
{
    public string Name { get; set; }
    public string KeyName { get; set; }
    public string Code { get; set; }

    private DecisionComponent(string code, string name, string keyName)
    {
        Code = code;
        Name = name;
        KeyName = keyName;
    }

    public static readonly DecisionComponent DecisionTable = new("dbx", DTABLE_NAME, "dt_code");
    public static readonly DecisionComponent DecisionTree = new("dtx", DTREE_NAME, "dtr_code");
    public static readonly DecisionComponent DecisionTreeNew = new(
        "dtxdtr",
        DTREE_NAME,
        "dtr_code"
    );
    public static readonly DecisionComponent ScoreCard = new("scx", SCORECARD_NAME, "scc_code");
    public static readonly DecisionComponent RuleSet = new("crx", RULESET_NAME, "ruleset_code");
    public static readonly DecisionComponent Rule = new("rlx", RULE_NAME, "rul_code");
    public static readonly DecisionComponent RuleCondition = new("que", RULE_NAME, "rul_code");
    public static readonly DecisionComponent DecisionFlow = new("d", DF_NAME, "df_code");
    public static readonly DecisionComponent PredefineIntegration = new(
        "xtr",
        XTR_NAME,
        "xtr_code"
    );
    public static readonly DecisionComponent FBE = new("fbe", FBE_NAME, "fbe_code");
    public static readonly DecisionComponent MachineLearning = new("mle", MLE_NAME, "mle_code");
    public static readonly DecisionComponent CreditBiro = new("cbx", CBX_NAME, "cbx_code");

    public static readonly DecisionComponent ChampionChallenger = new("ccx", CCX_NAME, "ccx_code");

    public static readonly DecisionComponent[] AllComponentDE =
    [
        DecisionTable,
        DecisionTree,
        DecisionTreeNew,
        Rule,
        RuleCondition,
        RuleSet,
        ScoreCard,
        DecisionFlow,
        PredefineIntegration,
        FBE,
        MachineLearning,
        CreditBiro,
        ChampionChallenger,
    ];

    public static readonly DecisionComponent[] SingleStepComponentDE =
    [
        ScoreCard,
        DecisionFlow,
        RuleCondition,
        RuleSet,
        FBE,
        MachineLearning,
        CreditBiro,
        PredefineIntegration,
        ChampionChallenger,
    ];

    public static readonly DecisionComponent[] CombinedStepComponentDE =
    [
        DecisionTable,
        DecisionTree,
        DecisionTreeNew,
        RuleSet,
        Rule,
    ];

    public static readonly DecisionComponent[] CouchbaseComponentDE = [RuleCondition, DecisionFlow];

    public static DecisionComponent GetByCode(string code) =>
        Array.Find(
            AllComponentDE,
            x => code.StartsWith(x.Code, StringComparison.CurrentCultureIgnoreCase)
        ) ?? throw new DataException($"{code} Is invalid component");

    public static DecisionComponent GetByCodePrecise(string code) =>
        Array.Find(
            array: AllComponentDE,
            match: component =>
                string.Join(separator: "", values: code.TakeWhile(char.IsLetter))
                    .Equals(
                        value: component.Code,
                        comparisonType: StringComparison.CurrentCultureIgnoreCase
                    )
        ) ?? throw new DataException($"{code} Is invalid component");

    public static DecisionComponent GetByKeyName(string keyName) =>
        Array.Find(
            AllComponentDE,
            x => keyName.Equals(x.KeyName, StringComparison.CurrentCultureIgnoreCase)
        ) ?? throw new DataException($"{keyName} Is invalid component");

    public const string SCORECARD_NAME = "Score Card";
    public const string DTABLE_NAME = "Decision Table";
    public const string DTREE_NAME = "Decision Tree";
    public const string RULE_NAME = "Rule";
    public const string RULESET_NAME = "Rule Set";
    public const string DF_NAME = "Decision Flow";
    public const string XTR_NAME = "Predefine Integration";
    public const string FBE_NAME = "FBE";
    public const string MLE_NAME = "Machine Learning";
    public const string CBX_NAME = "Credit Biro";
    public const string CCX_NAME = "Champion Challenger";
}
