using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;

namespace idc.decision_cb.Utilities.Models.DecisionFlow.Output;

public class DecisionFlowResultDecision
{
    [JsonProperty("field_name")]
    [Required(ErrorMessage = "DfResultDecision: FieldName is required.")]
    public required string FieldName { get; set; }

    [JsonProperty("field_value", NullValueHandling = NullValueHandling.Ignore)]
    public dynamic? FieldValue { get; set; }
}
