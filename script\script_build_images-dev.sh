#!/bin/bash

export TERM=xterm

source /apps/gitlab-runner/builds/mHDMZetj/0/idecision_source_net8/idc.decision_cb/Deployment

export $(cut -d= -f1 /apps/gitlab-runner/builds/mHDMZetj/0/idecision_source_net8/idc.decision_cb/Deployment)

sed -i "s/LABEL version=\"v1.0.0\"/LABEL version=\"$VERSION\"/g" /apps/gitlab-runner/builds/mHDMZetj/0/idecision_source_net8/idc.decision_cb/Dockerfile

cd /apps/gitlab-runner/builds/mHDMZetj/0/idecision_source_net8/"$PROJECT"/ && sudo docker build -t $DOCKERNIMAGES . --network=host

sleep 1;

sudo docker push $DOCKERNIMAGES
sudo docker tag $DOCKERNIMAGES $DOCKERNIMAGESLS
sudo docker push $DOCKERNIMAGESLS

docker rmi -f $(docker images | grep "<none>" | grep -v "portainer/agent" | awk '{print $3}')

CHKBUILD1=`sudo docker images | grep -w "$PROJECT" | grep -w hub.idecision.ai:8443 |awk '{print $1}' | grep -v latest | head -1`
CHKBUILD2=`sudo docker images | grep -w "$PROJECT" | grep -w hub.idecision.ai:8443 |awk '{print $2}' | head -1`

if [ "$CBUILD" = "$CHKBUILD1" ] || [ "v$VERSION" = "$CHKBUILD2" ] ; then


# sudo ssh -P2224 root@************ "docker pull "$DOCKERNIMAGES""

echo "`date` || Build Image Success for $DOCKERNIMAGES" >> /apps/script/activity_deploy.log

sudo docker rmi -f $DOCKERNIMAGES
sudo docker rmi -f $DOCKERNIMAGESLS

else

echo "`date` || Build Image Failure $DOCKERNIMAGES" >> /apps/script/activity_deploy.log

sleep 660;

fi
