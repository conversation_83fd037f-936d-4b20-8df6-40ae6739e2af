﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;

namespace idc.decision_cb.Utilities.Models.General.Input;

public class ConfigComponentInputModel
{
    [JsonProperty("key_name")]
    [Required(ErrorMessage = "ConfigComponentInputModel: key name is required")]
    public required string KeyName { get; set; }

    [JsonProperty("key_value")]
    [Required(ErrorMessage = "ConfigComponentInputModel: key value is required")]
    public required string KeyValue { get; set; }
}
