using System.ComponentModel.DataAnnotations;
using System.Text;
using idc.decision_cb.Utilities.Constants;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace idc.decision_cb.Utilities.Models.ScoreCard.Input;

/// <summary>
/// Represents a score card configuration.
/// </summary>
/// <example>
/// {
///     "scc_code": "1",
///     "scc_version": "1.0",
///     "scc_additive": "0",
///     "scc_output": "final_score",
///     "characteristics": [
///         {
///             "char_id": "1",
///             "char_field": "field_1",
///             "char_type": "integer",
///             "char_default": "0",
///             "char_koef": "1.0",
///             "detail": [
///                 {
///                     "limit": "18",
///                     "score": "0",
///                     "baseline": "true"
///                 }
///             ]
///         }
///     ]
/// }
/// </example>
public class ScoreCardConfigurationModel
{
    /// <summary>
    /// Gets or sets the code.
    /// </summary>
    /// <value>
    /// The code.
    /// </value>
    [JsonProperty("scc_code")]
    [Required(ErrorMessage = "ScoreCardConfigurationModel: Code is required.")]
    public required string Code { get; set; }

    /// <summary>
    /// Gets or sets the version.
    /// </summary>
    /// <value>
    /// The version.
    /// </value>
    [JsonProperty("scc_version")]
    [Required(ErrorMessage = "ScoreCardConfigurationModel: Version Version is required.")]
    public required string Version { get; set; }

    /// <summary>
    /// Gets or sets the additive.
    /// </summary>
    /// <value>
    /// The additive.
    /// </value>
    [JsonProperty("scc_additive")]
    [Required(ErrorMessage = "ScoreCardConfigurationModel: Additive is required.")]
    public required int Additive { get; set; }

    /// <summary>
    /// Gets or sets the output.
    /// </summary>
    /// <value>
    /// The output.
    /// </value>
    [JsonProperty("scc_output")]
    [Required(ErrorMessage = "ScoreCardConfigurationModel: Output is required.")]
    public required string Output { get; set; }

    /// <summary>
    /// Gets or sets the characteristics.
    /// </summary>
    /// <value>
    /// The characteristics.
    /// </value>
    [JsonProperty("characteristics")]
    [Required(ErrorMessage = "ScoreCardConfigurationModel: Characteristics is required.")]
    public required List<ScoreCardCharacteristicModel> Characteristics { get; set; }

    /// <summary>
    /// Generates a query to calculate the score based on the ScoreCard characteristics.
    /// </summary>
    /// <returns>A query string for calculating the score.</returns>
    /// <example>
    /// <code>
    /// var query = scoreCardConfigurationModel.SourceFieldsList();
    /// Console.WriteLine(query);
    /// </code>
    /// </example>
    public string SourceFieldsString()
    {
        List<string> listChar = [];

        Characteristics.ForEach(x =>
        {
            string? quote = x.Type.StartsWith(
                value: "varchar",
                comparisonType: StringComparison.CurrentCultureIgnoreCase
            )
                ? "'"
                : null;

            string comparison = x.Type.StartsWith(
                value: "varchar",
                comparisonType: StringComparison.CurrentCultureIgnoreCase
            )
                ? "="
                : "<=";

            StringBuilder rawCW = new();

            foreach (ScoreCardCharacteristicDetailModel detail in x.Detail)
            {
                rawCW
                    .Append(
                        detail.Limit.Equals(
                            value: "null",
                            comparisonType: StringComparison.CurrentCultureIgnoreCase
                        )
                            ? $" WHEN ({x.Field} is null) THEN "
                            : $" WHEN ({x.Field} {comparison} {quote}{detail.Limit}{quote}) THEN "
                    )
                    .Append(detail.Score);
            }
            listChar.Add($"((CASE {rawCW} ELSE {x.Default} END) * {x.Koef})");
        });

        return $@"{Output} = (({string.Join(" + ", listChar)}) + {Additive})";
    }

    public JObject ScorecardChars()
    {
        JObject listChar = [];

        Characteristics.ForEach(x =>
        {
            string charName = x.Field;
            string? quote = x.Type.StartsWith(
                value: "varchar",
                comparisonType: StringComparison.CurrentCultureIgnoreCase
            )
                ? "'"
                : null;

            string comparison = x.Type.StartsWith(
                value: "varchar",
                comparisonType: StringComparison.CurrentCultureIgnoreCase
            )
                ? "="
                : "<=";

            StringBuilder rawCW = new();

            foreach (ScoreCardCharacteristicDetailModel detail in x.Detail)
            {
                rawCW
                    .Append(
                        detail.Limit.Equals("null", StringComparison.CurrentCultureIgnoreCase)
                            ? $" WHEN ({x.Field} is null) THEN "
                            : $" WHEN ({x.Field} {comparison} {quote}{detail.Limit}{quote}) THEN "
                    )
                    .Append(detail.Score);
            }

            listChar[charName] = $"(CASE {rawCW} ELSE {x.Default} END) * {x.Koef}";
        });

        return listChar;
    }

    /// <summary>
    /// Generates a list of SQL query strings to calculate the score based on the ScoreCard characteristics.
    /// </summary>
    /// <returns>
    /// A list of SQL query strings for calculating the score.
    /// </returns>
    /// <example>
    /// <code>
    /// var queries = scoreCardConfigurationModel.SourceFieldsList();
    /// Console.WriteLine(string.Join(Environment.NewLine, queries));
    /// </code>
    /// </example>
    public List<string> SourceFieldsList() => [SourceFieldsString()];

    /// <summary>
    /// Generates a SQL query string to update the score card data.
    /// </summary>
    /// <returns>
    /// A SQL query string that updates the score card data with calculated scores.
    /// </returns>
    /// <example>
    /// var query = scoreCardConfig.ToQuery();
    /// </example>
    public string ToQuery()
    {
        return @$"UPDATE {CBCollectionsPath.CB_PATH_DATA_TEMP_TABLE}  "
            + $@"SET {SourceFieldsString()} "
            + @"WHERE cf_los_app_no = '{{IdTempTable}}' "
            + $@"RETURNING {Output};";
    }
}
