using idc.decision_cb.Utilities;
using idc.decision_cb.Utilities.Constants;
using idc.decision_cb.Utilities.Extensions;
using idc.decision_cb.Utilities.Helpers;
using idc.decision_cb.Utilities.Models.DecisionFlow.Input;
using idc.decision_cb.Utilities.Models.ScoreCard.Input;
using IDX.Utilities;
using IDX.Utilities.DataProcessor;
using IDX.Utilities.Models.API;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using NpgsqlTypes;

namespace idc.decision_cb.Controllers;

[Route("api/[controller]")]
[ApiController]
public class QueryGeneratorController(SystemLogging logging, JToken language, PgSqlHelper pgHelper)
    : ControllerBase
{
    private readonly string WorkflowScheme = "workflow";

    /// <summary>
    /// Processes a decision flow configuration and generates the corresponding output model.
    /// </summary>
    /// <remarks>
    /// This method processes the provided decision flow configuration and utilizes the input model
    /// to generate an output model that reflects the current state of the decision flow.
    /// </remarks>
    /// <param name="inputJson">The decision flow configuration input model.</param>
    /// <param name="cancellationToken">The cancellation token for the operation.</param>
    /// <returns>The generated decision flow configuration output model.</returns>
    /// <example>
    /// <code>
    /// {
    ///     "header": {
    ///         "flows_id": "1",
    ///         "name": "Decision Flow 1",
    ///         "description": "This is the first decision flow."
    ///     },
    ///     "nodes": [
    ///         {
    ///             "node_id": "1",
    ///             "node_name": "Node 1",
    ///             "node_type": "start",
    ///             "node_next": "2"
    ///         },
    ///         {
    ///             "node_id": "2",
    ///             "node_name": "Node 2",
    ///             "node_type": "end",
    ///             "node_next": "3"
    ///         },
    ///         {
    ///             "node_id": "3",
    ///             "node_name": "Node 3",
    ///             "node_type": "end",
    ///             "node_next": null
    ///         }
    ///     ],
    ///     "edges": [
    ///         {
    ///             "edge_id": "1",
    ///             "edge_source": "1",
    ///             "edge_target": "2",
    ///             "edge_label": "Edge 1"
    ///         },
    ///         {
    ///             "edge_id": "2",
    ///             "edge_source": "2",
    ///             "edge_target": "3",
    ///             "edge_label": "Edge 2"
    ///         }
    ///     ]
    /// }
    /// </code>
    /// </example>
    /// <response code="200">The generated decision flow configuration output model.</response>
    /// <response code="400">The request body is invalid.</response>
    [HttpPost("DecisionFlow")]
    public async Task<ApiResponseData<JObject?>> DecisionFlow(
        [FromBody] DecisionFlowConfigurationInputModel inputJson,
        CancellationToken cancellationToken = default
    )
    {
        ApiResponseData<JObject?> result = new();

        try
        {
            await Task.Run(
                () =>
                {
                    inputJson.Edges = inputJson
                        ?.Edges?.Where(predicate: x =>
                            !x.Target.Equals(
                                value: "end",
                                comparisonType: StringComparison.CurrentCultureIgnoreCase
                            )
                        )
                        .ToList();

                    Utilities.Models.DecisionFlow.Output.DecisionFlowConfigurationOutputModel data =
                        inputJson!.GenerateCouchbaseOutput(pgHelper: pgHelper);

                    result
                        .ChangeData(
                            data: pgHelper
                                .WriteComponentsConfiguration<string?>(
                                    keyName: "df_code",
                                    keyValue: data.DFCode,
                                    configurationJson: JsonConvert.SerializeObject(data)
                                )
                                .ToJObject()
                        )
                        .ChangeStatus(
                            languageToken: language,
                            propertyName: GeneralConstant.API_RESP_REQ_SUCCESS
                        )
                        .ChangeMessage(
                            languageToken: language,
                            propertyName: GeneralConstant.API_RESP_QUE_BUILDER_SUCCESS
                        );
                },
                cancellationToken: cancellationToken
            );
        }
        catch (Exception ex)
        {
            result
                .ChangeStatus(
                    languageToken: language,
                    propertyName: GeneralConstant.API_RESP_FAILED
                )
                .ChangeMessage(
                    exception: ex,
                    logging: logging,
                    includeStackTrace: Commons.IsDevelopmentMode()
                );
        }
        return result;
    }

    /// <summary>
    /// Generates a decision table query based on the provided table ID.
    /// </summary>
    /// <param name="dTableId">The ID of the decision table to generate a query for.</param>
    /// <param name="cancellationToken">The cancellation token for the operation.</param>
    /// <returns>An ApiResponseData&lt;JObject&gt; containing the result of the query generation.</returns>
    /// <remarks>
    /// This method utilizes a stored procedure to generate a decision table query
    /// and returns the result as a JSON object. It handles exceptions by updating the response
    /// status and message with error details.
    /// </remarks>
    /// <response code="200">The decision table query was generated successfully.</response>
    /// <response code="400">The request was invalid or failed to generate the query.</response>

    [HttpPost("DecisionTable/{dTableId}")]
    public async Task<ApiResponseData<JObject>> DecisionTable(
        int dTableId,
        CancellationToken cancellationToken = default
    )
    {
        ApiResponseData<JObject> result = new();

        try
        {
            await Task.Run(
                () =>
                {
                    pgHelper
                        .Connect()
                        .ExecuteScalar<string?>(
                            spCallInfo: new()
                            {
                                Schema = WorkflowScheme,
                                SPName = "cb_dtable_generate_query",
                                Parameters =
                                [
                                    new()
                                    {
                                        Name = "p_id",
                                        Value = dTableId,
                                        DataType = NpgsqlDbType.Bigint,
                                    },
                                ],
                            },
                            result: out string? output
                        );

                    result
                        .ChangeStatus(
                            languageToken: language,
                            propertyName: GeneralConstant.API_RESP_SUCCESS
                        )
                        .ChangeMessage(
                            languageToken: language,
                            propertyName: GeneralConstant.API_RESP_QUE_BUILDER_SUCCESS
                        )
                        .ChangeData(data: output.ToJObject());
                },
                cancellationToken: cancellationToken
            );
        }
        catch (Exception ex)
        {
            result
                .ChangeStatus(languageToken: language, propertyName: "fail")
                .ChangeMessage(
                    exception: ex,
                    logging: logging,
                    includeStackTrace: Commons.IsDevelopmentMode()
                );
        }

        return result;
    }

    /// <summary>
    /// Generates a query for a given Decision Tree configuration.
    /// </summary>
    /// <remarks>
    /// This API endpoint is used by the Decision Tree generator in IDX Decision Flow.
    /// It takes a Decision Tree configuration as input and returns the generated query.
    /// </remarks>
    /// <param name="dTreeId">The Decision Tree configuration to generate a query for.</param>
    /// <param name="cancellationToken">The cancellation token for the operation.</param>
    /// <returns>The generated query.</returns>
    /// <response code="200">Returns the generated query.</response>
    /// <response code="400">The request body is invalid.</response>
    [HttpPost("DecisionTree/{dTreeId}")]
    public async Task<ApiResponseData<JObject>> DecisionTree(
        int dTreeId,
        CancellationToken cancellationToken = default
    )
    {
        ApiResponseData<JObject> result = new();

        try
        {
            await Task.Run(
                action: () =>
                {
                    pgHelper
                        .Connect()
                        .ExecuteScalar<string?>(
                            spCallInfo: new()
                            {
                                Schema = WorkflowScheme,
                                SPName = "cb_dtree_generate_query",
                                Parameters =
                                [
                                    new()
                                    {
                                        Name = "p_id",
                                        Value = dTreeId,
                                        DataType = NpgsqlDbType.Bigint,
                                    },
                                ],
                            },
                            result: out string? output
                        );

                    result
                        .ChangeStatus(
                            languageToken: language,
                            propertyName: GeneralConstant.API_RESP_SUCCESS
                        )
                        .ChangeMessage(
                            languageToken: language,
                            propertyName: GeneralConstant.API_RESP_QUE_BUILDER_SUCCESS
                        )
                        .ChangeData(data: output.ToJObject());
                },
                cancellationToken: cancellationToken
            );
        }
        catch (Exception ex)
        {
            result
                .ChangeStatus(
                    languageToken: language,
                    propertyName: GeneralConstant.API_RESP_FAILED
                )
                .ChangeMessage(
                    exception: ex,
                    logging: logging,
                    includeStackTrace: Commons.IsDevelopmentMode()
                );
        }

        return result;
    }

    /// <summary>
    /// Generates a query for a given Rule configuration.
    /// </summary>
    /// <remarks>
    /// The response will be a JSON object with key `query` containing the generated query string.
    /// </remarks>
    /// <param name="ruleId">The Rule configuration to generate a query for.</param>
    /// <param name="cancellationToken">The cancellation token for the operation.</param>
    /// <returns>The generated query.</returns>
    /// <example>
    /// <code>
    /// {
    ///     "id": 1
    /// }
    /// </code>
    /// </example>
    /// <response code="200">Returns the generated query.</response>
    /// <response code="400">The request body is invalid.</response>
    [HttpPost("Rule/{ruleId}")]
    public async Task<ApiResponseData<JObject>> Rule(
        int ruleId,
        CancellationToken cancellationToken = default
    )
    {
        ApiResponseData<JObject> result = new();

        try
        {
            await Task.Run(
                action: () =>
                {
                    pgHelper
                        .Connect()
                        .ExecuteScalar<string?>(
                            spCallInfo: new()
                            {
                                Schema = WorkflowScheme,
                                SPName = "cb_rule_generate_query",
                                Parameters =
                                [
                                    new()
                                    {
                                        Name = "p_rule_id",
                                        Value = ruleId,
                                        DataType = NpgsqlDbType.Integer,
                                    },
                                    new()
                                    {
                                        Name = "p_temp_table",
                                        Value = CBCollectionsPath.CB_PATH_DATA_TEMP_TABLE,
                                        DataType = NpgsqlDbType.Text,
                                    },
                                ],
                            },
                            result: out string? output
                        );

                    result
                        .ChangeStatus(
                            languageToken: language,
                            propertyName: GeneralConstant.API_RESP_SUCCESS
                        )
                        .ChangeMessage(
                            languageToken: language,
                            propertyName: GeneralConstant.API_RESP_QUE_BUILDER_SUCCESS
                        )
                        .ChangeData(data: output.ToJObject());
                },
                cancellationToken: cancellationToken
            );
        }
        catch (Exception ex)
        {
            result
                .ChangeStatus(
                    languageToken: language,
                    propertyName: GeneralConstant.API_RESP_FAILED
                )
                .ChangeMessage(
                    exception: ex,
                    logging: logging,
                    includeStackTrace: Commons.IsDevelopmentMode()
                );
        }

        return result;
    }

    /// <summary>
    /// Generates a query for a given RuleSet configuration.
    /// </summary>
    /// <remarks>
    /// This endpoint generates a query for a given RuleSet configuration. The query can be used to execute the RuleSet against a given input.
    /// </remarks>
    /// <param name="ruleSetId">The RuleSet configuration to generate a query for.</param>
    /// <param name="cancellationToken">The cancellation token for the operation.</param>
    /// <returns>The generated query.</returns>
    /// <example>
    /// <code>
    /// {
    ///     "id": 1
    /// }
    /// </code>
    /// </example>
    /// <response code="200">Returns the generated query.</response>
    /// <response code="400">The request body is invalid.</response>
    [HttpPost("RuleSet/{ruleSetId}")]
    public async Task<ApiResponseData<JObject>> RuleSet(
        long ruleSetId,
        CancellationToken cancellationToken = default
    )
    {
        ApiResponseData<JObject> result = new();

        try
        {
            await Task.Run(
                action: () =>
                {
                    pgHelper
                        .Connect()
                        .ExecuteScalar<string?>(
                            spCallInfo: new()
                            {
                                Schema = WorkflowScheme,
                                SPName = "cb_ruleset_generate_query",
                                Parameters =
                                [
                                    new()
                                    {
                                        Name = "p_ruleset_id",
                                        Value = ruleSetId,
                                        DataType = NpgsqlDbType.Bigint,
                                    },
                                    new()
                                    {
                                        Name = "p_temp_table",
                                        Value = CBCollectionsPath.CB_PATH_DATA_TEMP_TABLE,
                                        DataType = NpgsqlDbType.Text,
                                    },
                                ],
                            },
                            result: out string? output
                        );

                    result
                        .ChangeStatus(
                            languageToken: language,
                            propertyName: GeneralConstant.API_RESP_SUCCESS
                        )
                        .ChangeMessage(
                            languageToken: language,
                            propertyName: GeneralConstant.API_RESP_QUE_BUILDER_SUCCESS
                        )
                        .ChangeData(data: output.ToJObject());
                },
                cancellationToken: cancellationToken
            );
        }
        catch (Exception ex)
        {
            result
                .ChangeStatus(
                    languageToken: language,
                    propertyName: GeneralConstant.API_RESP_FAILED
                )
                .ChangeMessage(
                    exception: ex,
                    logging: logging,
                    includeStackTrace: Commons.IsDevelopmentMode()
                );
        }

        return result;
    }

    /// <summary>
    /// Generates a query for a given ScoreCard configuration.
    /// </summary>
    /// <remarks>
    /// This method will generate a query based on the given ScoreCard configuration.
    /// The query can be used to evaluate the ScoreCard for a given input.
    /// </remarks>
    /// <param name="scorecardJson">The ScoreCard configuration to generate a query for.</param>
    /// <param name="cancellationToken">The cancellation token for the operation.</param>
    /// <returns>The generated query.</returns>
    /// <example>
    /// <code>
    /// {
    ///     "code": "MyScoreCard",
    ///     "version": "1.0",
    ///     "additive": 0,
    ///     "output": "final_score",
    ///     "characteristics": [
    ///         {
    ///             "code": "Age",
    ///             "field": "applicant_age",
    ///             "type": "integer",
    ///             "koef": 1,
    ///             "detail": [
    ///                 {
    ///                     "limit": 18,
    ///                     "score": 0
    ///                 },
    ///                 {
    ///                     "limit": 25,
    ///                     "score": 10
    ///                 },
    ///                 {
    ///                     "limit": 35,
    ///                     "score": 20
    ///                 },
    ///                 {
    ///                     "limit": 45,
    ///                     "score": 30
    ///                 },
    ///                 {
    ///                     "limit": null,
    ///                     "score": 40
    ///                 }
    ///             ]
    ///         }
    ///     ]
    /// }
    /// </code>
    /// </example>
    /// <response code="200">Returns the generated query.</response>
    /// <response code="400">The request body is invalid.</response>
    [HttpPost("ScoreCard")]
    public async Task<ApiResponseData<JObject>> ScoreCard(
        [FromBody] ScoreCardConfigurationModel scorecardJson,
        CancellationToken cancellationToken = default
    )
    {
        ApiResponseData<JObject> result = new();

        try
        {
            await Task.Run(
                () =>
                {
                    result
                        .ChangeStatus(
                            languageToken: language,
                            propertyName: GeneralConstant.API_RESP_SUCCESS
                        )
                        .ChangeMessage(
                            languageToken: language,
                            propertyName: GeneralConstant.API_RESP_QUE_BUILDER_SUCCESS
                        )
                        .ChangeData(
                            data: pgHelper
                                .WriteComponentsConfiguration<string?>(
                                    keyName: "scc_code",
                                    keyValue: scorecardJson.Code.ToUpper().Replace("SCC", "SCX"),
                                    configurationJson: JsonConvert.SerializeObject(
                                        new
                                        {
                                            scc_code = scorecardJson
                                                .Code.ToUpper()
                                                .Replace("SCC", "SCX"),
                                            scc_version = scorecardJson.Version,
                                            scc_additive = scorecardJson.Additive,
                                            scc_output = scorecardJson.Output,
                                            src_fields = scorecardJson.SourceFieldsList(),
                                            scc_chars = scorecardJson.ScorecardChars(),
                                            no_sql = scorecardJson.ToQuery(),
                                        }
                                    )
                                )
                                .ToJObject()
                        );
                },
                cancellationToken: cancellationToken
            );
        }
        catch (Exception ex)
        {
            result
                .ChangeStatus(
                    languageToken: language,
                    propertyName: GeneralConstant.API_RESP_FAILED
                )
                .ChangeMessage(
                    exception: ex,
                    logging: logging,
                    includeStackTrace: Commons.IsDevelopmentMode()
                );
        }

        return result;
    }
}
