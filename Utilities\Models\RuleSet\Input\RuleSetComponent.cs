using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;

namespace idc.decision_cb.Utilities.Models.RuleSet.Input;

public partial class RuleSetComponent
{
    [JsonProperty("component_code")]
    [Required(ErrorMessage = "RuleSetComponent: Component code is required")]
    public required string ComponentCode { get; set; }

    [JsonProperty("component_type")]
    [Required(ErrorMessage = "RuleSetComponent: Component type is required")]
    public required string ComponentType { get; set; }

    [JsonProperty("detail")]
    public RuleSetComponentDetail[]? Detail { get; set; }
}
