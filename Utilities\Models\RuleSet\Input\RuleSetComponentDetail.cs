using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;

namespace idc.decision_cb.Utilities.Models.RuleSet.Input;

public partial class RuleSetComponentDetail
{
    [JsonProperty("no_sql")]
    [Required(ErrorMessage = "RuleSetComponentDetail: No sql is required")]
    public required string NoSql { get; set; }

    [JsonProperty("output")]
    [Required(ErrorMessage = "RuleSetComponentDetail: Output is required")]
    public required string Output { get; set; }

    [JsonProperty("time_constraint")]
    [Required(ErrorMessage = "RuleSetComponentDetail: Time constraint is required")]
    public required string TimeConstraint { get; set; }
}
