using System.Data;
using idc.decision_cb.Utilities.Extensions;
using IDX.Utilities;
using Newtonsoft.Json.Linq;

namespace idc.decision_cb.Utilities.Validations;

public static class CommonValidations
{
    /// <summary>
    /// Ensures the stored procedure output indicates success, otherwise throws.
    /// </summary>
    /// <param name="output">Output to check.</param>
    /// <param name="langToken">Language token for parsing output.</param>
    /// <param name="dataMap">Optional data map for binding.</param>
    /// <exception cref="DataException">Thrown when output is not success.</exception>
    internal static void EnsureSPOutput(
        this string? output,
        JToken langToken,
        Dictionary<string, object>? dataMap
    )
    {
        if (!string.IsNullOrWhiteSpace(output) && output != "success")
            throw new DataException(
                output.ParseLanguageToken(langToken: langToken).BindWith(dataMap ?? [])
            );
    }

    /// <summary>
    /// Validates that the properties in the JSON object exist and have values if specified.
    /// Throws a DataException if any property is missing or has an empty value.
    /// </summary>
    /// <param name="obj">The JSON object to validate.</param>
    /// <param name="props">The properties to validate.</param>
    /// <param name="langToken">The language token for parsing output.</param>
    /// <param name="validateValue">(Optional) Whether to include the value check. Default is true.</param>
    /// <exception cref="DataException">Thrown when a property is missing or has an empty value.</exception>
    internal static void ValidateProps(
        this JObject? obj,
        string[] props,
        JToken langToken,
        bool validateValue = true
    )
    {
        if (obj is null)
            throw new DataException("json_is_empty".ParseLanguageToken(langToken: langToken));

        foreach (string prop in props)
        {
            if (!validateValue)
            {
                if (!obj!.ContainsKey(prop))
                    throw new DataException(
                        "json_props_incomplete".ParseLanguageToken(
                            langToken: langToken,
                            replacement: new Dictionary<string, object>() { { "{field}", prop } }
                        )
                    );
            }
            else
            {
                if (!obj!.ContainsKey(prop) || obj![prop] is null || obj![prop]?.ToString() == "")
                    throw new DataException(
                        "json_props_invalid".ParseLanguageToken(
                            langToken: langToken,
                            replacement: new Dictionary<string, object>() { { "{field}", prop } }
                        )
                    );
            }
        }
    }
}
