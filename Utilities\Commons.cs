using idc.decision_cb.Utilities.Constants;
using idc.decision_cb.Utilities.Middlewares;
using IDX.Utilities;
using Newtonsoft.Json.Linq;

namespace idc.decision_cb.Utilities;

public static class Commons
{
    /// <summary>
    /// Builds the ConfigurationRoot using the provided relative path to the JSON file.
    /// </summary>
    /// <param name="relativePath">The relative path to the JSON file.</param>
    /// <returns>The ConfigurationRoot object.</returns>
    /// <exception cref="ArgumentNullException">Thrown when the relativePath is null.</exception>
    /// <exception cref="FileNotFoundException">Thrown when the JSON file at the relative path is not found.</exception>
    internal static IConfigurationRoot ConfigJsonBuilder(string relativePath) =>
        new ConfigurationBuilder()
            .SetBasePath(Directory.GetCurrentDirectory())
            .AddJsonFile(relativePath, optional: true, reloadOnChange: true)
            .Build();

    /// <summary>
    /// Retrieves the configuration data from the config_de table in the
    /// sqlite database file. If the data is not found, it will execute the
    /// callback function and store the result in the database.
    /// </summary>
    /// <param name="docId">The name of the configuration data to retrieve.</param>
    /// <param name="iSqlite">The sqlite database file to use.</param>
    /// <returns>The configuration data as a JObject, or null if the data is not found.</returns>
    /// <example>
    /// <code>
    /// var config = GetConfigDE(
    ///     name: "my-config",
    ///     iSqlite: new MultipleSqliteInstance("my-sqlite.db"),
    ///     callback: () => new JObject
    ///     {
    ///         ["key"] = "value"
    ///     }
    /// );
    /// Console.WriteLine(config);
    /// </code>
    /// </example>
    internal static JObject? GetConfigDE(string docId, MultipleSqliteInstance iSqlite)
    {
        iSqlite
            .File.Connect()
            .ExecuteScalar(
                query: GeneralConstant.DB_DF_CONFIG_SELECT_QUERY(docId),
                result: out object? data
            );

        return data is string dataString && !string.IsNullOrWhiteSpace(dataString)
            ? JObject.Parse(dataString)
            : null;
    }

    /// <summary>
    /// Retrieves the parameter result as a string from the sqlite database using the specified document ID.
    /// </summary>
    /// <param name="docId">The document ID to query for parameter results.</param>
    /// <param name="iSqlite">The sqlite database instance to execute the query on.</param>
    /// <returns>The parameter result as a string.</returns>
    internal static string GetParameterResult(string docId, MultipleSqliteInstance iSqlite)
    {
        iSqlite
            .File.Connect()
            .ExecuteScalar(
                query: GeneralConstant.DB_DF_PARAMRESULTS_SELECT_QUERY(docId),
                result: out object? data
            );

        return data.CastToString();
    }

    /// <summary>
    /// Retrieves a message from the specified message name and language.
    /// </summary>
    /// <param name="messageName">The name of the message to retrieve.</param>
    /// <param name="lang">The language of the message (optional, default is "default").</param>
    /// <returns>The retrieved message or the message name if it is not found.</returns>
    /// <exception cref="ArgumentNullException">Thrown when the messageName is null or whitespace.</exception>
    internal static string? GetMessage(string? messageName, string? lang = "default")
    {
        if (string.IsNullOrWhiteSpace(messageName))
            throw new ArgumentNullException(nameof(messageName));

        lang =
            lang == "default"
                ? ReadConfigurationValue<string>("IndividualSettings:idc.DecisionCB:Language")
                    ?.ToLower()
                : lang;

        return ReadConfigurationValue<string>(
                name: messageName,
                filePath: $"wwwroot/Lang/{lang}.json"
            ) ?? messageName;
    }

    /// <summary>
    /// Returns a formatted message using the specified message name,
    /// data dictionary, and language.
    /// </summary>
    /// <param name="messageName">The name of the message to retrieve.</param>
    /// <param name="data">The data dictionary to bind with the message.</param>
    /// <param name="lang">The language for the message. Defaults to "default".</param>
    /// <returns>The formatted message.</returns>
    internal static string? GetMessageFormatWith(
        string? messageName,
        Dictionary<string, object>? data,
        string? lang = "default"
    )
    {
        if (string.IsNullOrWhiteSpace(messageName))
            throw new ArgumentNullException(nameof(messageName));

        return data is null
            ? GetMessage(messageName, lang)
            : (GetMessage(messageName, lang)?.BindWith(data));
    }

    /// <summary>
    /// Determines whether the application is running in development mode.
    /// </summary>
    /// <returns>
    /// <c>true</c> if the environment is set to development mode; otherwise, <c>false</c>.
    /// </returns>
    internal static bool IsDevelopmentMode() =>
        string.Equals(
            a: Environment.GetEnvironmentVariable(GeneralConstant.DBG_ENVIRONMENT),
            b: GeneralConstant.DBG_DEVELOPMENT,
            comparisonType: StringComparison.OrdinalIgnoreCase
        );

    /// <summary>
    /// Reads a configuration value from the specified file.
    /// </summary>
    /// <typeparam name="T">The type of the configuration value.</typeparam>
    /// <param name="name">The name of the configuration value.</param>
    /// <param name="filePath">The path to the configuration file. Default is "appsettings.json".</param>
    /// <returns>The configuration value.</returns>
    /// <exception cref="ArgumentNullException">Thrown when the name parameter is null, empty, or whitespace.</exception>
    internal static T? ReadConfigurationValue<T>(string name, string filePath = "appsettings.json")
    {
        if (string.IsNullOrWhiteSpace(name))
            throw new ArgumentNullException(nameof(name));

        return typeof(T).IsArray
            ? ConfigJsonBuilder(filePath).GetSection(name).Get<T>()
            : ConfigJsonBuilder(filePath).GetValue<T>(name);
    }

    /// <summary>
    /// Reads a configuration value from the specified configuration.
    /// </summary>
    /// <typeparam name="T">The type of the configuration value.</typeparam>
    /// <param name="config">The configuration to read the value from.</param>
    /// <param name="name">The name of the configuration value.</param>
    /// <returns>The configuration value.</returns>
    /// <exception cref="ArgumentNullException">Thrown when the name parameter is null, empty, or whitespace.</exception>
    internal static T? ReadConfigurationValue<T>(this IConfiguration config, string name)
    {
        if (string.IsNullOrWhiteSpace(name))
            throw new ArgumentNullException(nameof(name));

        return typeof(T).IsArray ? config.GetSection(name).Get<T>() : config.GetValue<T>(name);
    }

    /// <summary>
    /// Reads a configuration value from the specified configuration,
    /// or returns the default value if it is not found.
    /// </summary>
    /// <typeparam name="T">The type of the configuration value.</typeparam>
    /// <param name="config">The configuration to read the value from.</param>
    /// <param name="name">The name of the configuration value.</param>
    /// <param name="defaultValue">The default value to return if the configuration value is not found.</param>
    /// <returns>The configuration value, or the default value if it is not found.</returns>
    /// <exception cref="ArgumentNullException">Thrown when the name parameter is null, empty, or whitespace.</exception>
    internal static T ReadConfigurationValue<T>(
        this IConfiguration config,
        string name,
        T defaultValue
    )
    {
        T? value = config.ReadConfigurationValue<T>(name);
        if (value is null)
            return defaultValue;

        if (typeof(T) == typeof(string) && string.IsNullOrWhiteSpace(value as string))
            return defaultValue;

        return value;
    }

    /// <summary>
    /// Decrypts the given input text using the configured encryption key and salts.
    /// </summary>
    /// <param name="inputText">The input text to decrypt.</param>
    /// <returns>The decrypted text, or null if the input text is null or empty.</returns>
    internal static string? DecryptText(string? inputText)
    {
        using Encryptor encryptor = new Encryptor(
            encryptionKey: ReadConfigurationValue<string>("KeyConvert:EncryptionKey") ?? null,
            salts: ReadConfigurationValue<byte[]>("KeyConvert:EncryptionSalts") ?? null
        ).Decrypt(text: inputText, out string? outputText);
        return outputText;
    }
}
