using System.Net;
using System.Net.Http.Headers;
using System.Net.Security;
using System.Reflection;
using System.Security.Cryptography.X509Certificates;
using System.Text;
using idc.decision_cb.Utilities;
using idc.decision_cb.Utilities.Constants;
using idc.decision_cb.Utilities.Middlewares;
using IDX.Utilities;
using IDX.Utilities.Models.API;
using Microsoft.AspNetCore.Cors.Infrastructure;
using Microsoft.AspNetCore.Diagnostics;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Server.Kestrel.Core;
using Microsoft.Extensions.FileProviders;
using Microsoft.OpenApi.Models;
using Newtonsoft.Json;
using OpenTelemetry.Exporter;
using OpenTelemetry.Resources;
using OpenTelemetry.Trace;
using Swashbuckle.AspNetCore.SwaggerGen;
using Swashbuckle.AspNetCore.SwaggerUI;

namespace idc.decision_cb;

public static partial class Program
{
    public const string AppName = "idc.DecisionCB";
    public const string ParentSettingName = $"IndividualSettings:{AppName}";

    /// <summary>
    /// The entry point of the program, where the program control starts and ends.
    /// </summary>
    /// <param name="args">The command-line arguments.</param>
    public static void Main(string[] args)
    {
        IConfigurationRoot appSettings = Commons.ConfigJsonBuilder(
            relativePath: "appsettings.json"
        );
        WebApplicationBuilder builder = WebApplication.CreateBuilder(args: args);

        string appName = appSettings.ReadConfigurationValue(
            name: $"{ParentSettingName}:ApplicationName",
            defaultValue: "IDX Template"
        );

        SystemLogging logging = SystemLoggingActivator(appSettings: appSettings, appName: appName);

        OpenApiInfo oApiInfo = OpenApiInfoActivator(appSettings: appSettings, appName: appName);

        // Configure OpenTelemetry with tracing and auto-start.
        SetupOpenTelemetry(appSettings: appSettings, builder: builder, appName: appName);

        SetupConfiguration(configuration: builder.Configuration);
        SetupWebHost(webHost: builder.WebHost, configuration: builder.Configuration);
        SetupServices(services: builder.Services, openApiInfo: oApiInfo, appSettings: appSettings);
        SetupCors(services: builder.Services, appSettings: appSettings);
        SetupHttpHandlers(services: builder.Services);
        SetupDependencyInjections(
            services: builder.Services,
            logger: logging,
            appSettings: appSettings
        );
        SetupApp(builder: builder, oApiInfo: oApiInfo, logger: logging, appSettings: appSettings);
    }

    private static void SetupOpenTelemetry(
        IConfigurationRoot appSettings,
        WebApplicationBuilder builder,
        string appName
    )
    {
        builder
            .Services.AddOpenTelemetry()
            .ConfigureResource(resource => resource.AddService(serviceName: appName))
            .WithTracing(tracing =>
                tracing
                    .AddAspNetCoreInstrumentation()
                    .AddOtlpExporter(otlpOptions =>
                    {
                        otlpOptions.Endpoint = new Uri(
                            appSettings.ReadConfigurationValue<string>(
                                $"{ParentSettingName}:signoz-uri"
                            ) ?? "http://27.112.78.216:4317"
                        );
                        otlpOptions.Protocol = OtlpExportProtocol.Grpc;
                    })
                    .AddConsoleExporter()
            );
    }

    /// <summary>
    /// Activates the OpenAPI info based on the configuration settings.
    /// </summary>
    /// <param name="appSettings">The configuration settings.</param>
    /// <param name="appName">The application name.</param>
    /// <returns>The OpenAPI info.</returns>
    /// <remarks>
    /// This method initializes the OpenAPI information for the application using the specified configuration settings.
    /// The information includes title, version, description, terms of service, contact, and license details.
    /// </remarks>
    private static OpenApiInfo OpenApiInfoActivator(
        IConfigurationRoot appSettings,
        string appName
    ) =>
        new()
        {
            Title =
                $"{appName} - {appSettings.ReadConfigurationValue<string>($"{ParentSettingName}:Swagger:OpenAPIInfo:Title")}",
            Version = appSettings.ReadConfigurationValue<string>(
                $"{ParentSettingName}:Swagger:OpenAPIInfo:Version"
            ),
            Description = appSettings.ReadConfigurationValue<string>(
                $"{ParentSettingName}:Swagger:OpenAPIInfo:Description"
            ),
            TermsOfService = appSettings.ReadConfigurationValue<Uri>(
                "Swagger:OpenAPIInfo:TermsOfService"
            ),
            Contact = new OpenApiContact
            {
                Name = appSettings.ReadConfigurationValue<string>(
                    "Swagger:OpenAPIInfo:Contact:Name"
                ),
                Email = appSettings.ReadConfigurationValue<string>(
                    "Swagger:OpenAPIInfo:Contact:Email"
                ),
                Url = appSettings.ReadConfigurationValue<Uri>("Swagger:OpenAPIInfo:Contact:Url")
            },
            License = new OpenApiLicense
            {
                Name = appSettings.ReadConfigurationValue<string>(
                    "Swagger:OpenAPIInfo:License:Name"
                ),
                Url = appSettings.ReadConfigurationValue<Uri>("Swagger:OpenAPIInfo:License:Url")
            }
        };

    /// <summary>
    /// Activates the SystemLogging with the given app settings and app name.
    /// </summary>
    /// <param name="appSettings">The application settings.</param>
    /// <param name="appName">The name of the application.</param>
    /// <returns>The activated SystemLogging.</returns>
    /// <remarks>
    /// This method initializes the SystemLogging by reading the configuration values
    /// from the application settings, including the log file path, system write settings,
    /// and minimum log level.
    /// </remarks>
    private static SystemLogging SystemLoggingActivator(
        IConfigurationRoot appSettings,
        string appName
    ) =>
        new SystemLogging(
            logSource: appName,
            logFilePath: appSettings.ReadConfigurationValue(
                name: $"{ParentSettingName}:Logging:Config:FilePath",
                defaultValue: Path.Combine(
                    path1: AppContext.BaseDirectory,
                    path2: $"wwwroot/Logs/Logs.txt"
                )
            )
        )
            .WithSettingMinimumLevel(
                minimumLevel: (LogLevel)
                    Enum.Parse(
                        enumType: typeof(LogLevel),
                        value: appSettings.ReadConfigurationValue(
                            name: $"{ParentSettingName}:Logging:LogLevel:Default",
                            defaultValue: "Information"
                        )
                    )
            )
            .WithSettingsForceWriteToFile(
                forceWriteToFile: appSettings.ReadConfigurationValue<bool>(
                    $"{ParentSettingName}:Logging:Config:ForceWriteToFile"
                )
            )
            .WithSettingsWriteToSystem(
                writeToSystem: appSettings.ReadConfigurationValue<bool>(
                    $"{ParentSettingName}:Logging:Config:WriteToSystem"
                )
            );

    /// <summary>
    /// Validates the server certificate.
    /// </summary>
    /// <param name="request">The HTTP request message.</param>
    /// <param name="certificate">The server certificate.</param>
    /// <param name="chain">The X.509 chain.</param>
    /// <param name="sslPolicyErrors">The SSL policy errors.</param>
    /// <returns><c>true</c> to accept the certificate, <c>false</c> otherwise.</returns>
    /// <remarks>
    /// This callback is invoked by the <see cref="HttpClient"/> instance when a
    /// server certificate is presented to the client. The callback can be used
    /// to validate the server certificate.
    /// </remarks>
    private static bool ValidateServerCertificate(
        HttpRequestMessage request,
        X509Certificate2? certificate,
        X509Chain? chain,
        SslPolicyErrors sslPolicyErrors
    ) => true;

    /// <summary>
    /// Set up the configuration for the provided ConfigurationManager.
    /// </summary>
    /// <param name="configuration">The ConfigurationManager to set up.</param>
    /// <remarks>
    /// This method sets up the configuration by adding the 'hosting.json' file as an optional source.
    /// </remarks>
    private static void SetupConfiguration(ConfigurationManager configuration) =>
        configuration
            .SetBasePath(basePath: AppContext.BaseDirectory)
            .AddJsonFile(path: "hosting.json", optional: true);

    /// <summary>
    /// Sets up the web host with the provided configuration and content root.
    /// </summary>
    /// <param name="webHost">The web host builder.</param>
    /// <param name="configuration">The configuration manager.</param>
    /// <remarks>
    /// This method sets up the web host using the provided configuration manager.
    /// The method configures the web host to use the provided configuration and content root path.
    /// </remarks>
    private static void SetupWebHost(
        ConfigureWebHostBuilder webHost,
        ConfigurationManager configuration
    ) =>
        webHost
            .UseConfiguration(configuration: configuration)
            .UseKestrel(options: static options => options.AddServerHeader = false)
            .UseContentRoot(contentRoot: Directory.GetCurrentDirectory());

    /// <summary>
    /// Sets up the services for the API, including controllers, JSON serialization, Swagger documentation, and API behavior options.
    /// </summary>
    /// <param name="services">The service collection to add the services to.</param>
    /// <param name="openApiInfo">The OpenAPI info to use for Swagger documentation.</param>
    /// <param name="appSettings"></param>
    /// <remarks>
    /// This method sets up the services for the API, including controllers, JSON serialization, Swagger documentation, and API behavior options.
    /// </remarks>
    private static void SetupServices(
        IServiceCollection services,
        OpenApiInfo openApiInfo,
        IConfigurationRoot appSettings
    )
    {
        services
            .AddControllers(options =>
            {
                const string ContentType = GeneralConstant.API_CONTENT_TYPE;

                options.Filters.Add(filterType: typeof(ModelStateInvalidFilters));
                options.Filters.Add(item: new ConsumesAttribute(contentType: ContentType));
                options.Filters.Add(item: new ProducesAttribute(contentType: ContentType));
                options.Filters.Add(item: new ProducesResponseTypeAttribute(statusCode: 200));
                options.Filters.Add(
                    item: new ProducesResponseTypeAttribute(
                        type: typeof(ApiResponseData<string[]>),
                        statusCode: StatusCodes.Status400BadRequest
                    )
                );
                options.Filters.Add(
                    item: new ProducesResponseTypeAttribute(
                        type: typeof(ApiResponse),
                        statusCode: StatusCodes.Status500InternalServerError
                    )
                );
            })
            .AddNewtonsoftJson();

        services
            .AddEndpointsApiExplorer()
            .AddSwaggerGen(options =>
            {
                options.SwaggerDoc(openApiInfo.Version, openApiInfo);
                options.IncludeXmlComments(
                    filePath: Path.Combine(
                        path1: AppContext.BaseDirectory,
                        path2: $"{Assembly.GetExecutingAssembly().GetName().Name}.xml"
                    )
                );

                if (
                    appSettings.ReadConfigurationValue<bool>(
                        $"{ParentSettingName}:Swagger:UI:SortEndpoints"
                    )
                )
                    options.DocumentFilter<AlphabeticalSortDocumentFilter>();
            })
            .Configure<ApiBehaviorOptions>(configureOptions: options =>
            {
                options.SuppressModelStateInvalidFilter = true;
                options.SuppressMapClientErrors = true;
            });
    }

    /// <summary>
    /// Sets up the CORS policy for the application if enabled in the configuration.
    /// </summary>
    /// <param name="services">The service collection to add the services to.</param>
    /// <param name="appSettings">The application settings.</param>
    /// <remarks>
    /// This method sets up the CORS policy for the application if enabled in the configuration.
    /// The CORS policy is added to the service collection if the configuration value
    /// <c>idc.template.Cors.Enabled</c> is set to true.
    /// </remarks>
    private static void SetupCors(IServiceCollection services, IConfigurationRoot appSettings)
    {
        if (!appSettings.ReadConfigurationValue<bool>($"{ParentSettingName}:Cors:Enabled"))
            return;

        services.AddCors(setupAction: options =>
            options.AddDefaultPolicy(configurePolicy: builder =>
            {
                SetupCorsHosts(builder: builder, appSettings: appSettings);
                SetupCorsHeaders(builder: builder, appSettings: appSettings);
                SetupCorsMethods(builder: builder, appSettings: appSettings);
            })
        );
    }

    /// <summary>
    /// Configures the CORS policy for the application.
    /// </summary>
    /// <param name="builder">CORS policy builder.</param>
    /// <param name="appSettings"></param>
    /// <remarks>
    /// This method configures the CORS policy for the application.
    /// The method reads the configuration values for the CORS policy
    /// from the application settings and configures the CORS policy
    /// builder accordingly.
    /// </remarks>
    private static void SetupCorsHosts(CorsPolicyBuilder builder, IConfigurationRoot appSettings)
    {
        string[]? allowedHost = appSettings.ReadConfigurationValue<string[]>(
            $"{ParentSettingName}:Cors:AllowedHosts"
        );
        if (allowedHost != null && allowedHost.Length > 0)
            builder.WithOrigins(allowedHost);
        else
            builder.AllowAnyOrigin();
    }

    /// <summary>
    /// Configures the allowed headers for the CORS policy.
    /// </summary>
    /// <param name="builder">The CORS policy builder.</param>
    /// <param name="appSettings">The application settings.</param>
    /// <remarks>
    /// This method sets up the allowed headers for the CORS policy by reading
    /// the configuration values from the application settings. If no specific
    /// headers are configured, it allows any header.
    /// </remarks>
    private static void SetupCorsHeaders(CorsPolicyBuilder builder, IConfigurationRoot appSettings)
    {
        string[]? allowedHeaders = appSettings.ReadConfigurationValue<string[]>(
            $"{ParentSettingName}:Cors:AllowedHeaders"
        );
        if (allowedHeaders != null && allowedHeaders.Length > 0)
            builder.WithHeaders(allowedHeaders);
        else
            builder.AllowAnyHeader();
    }

    /// <summary>
    /// Sets up the allowed HTTP methods for the CORS policy.
    /// </summary>
    /// <param name="builder">The CORS policy builder.</param>
    /// <param name="appSettings">The application settings.</param>
    /// <remarks>
    /// This method sets up the allowed HTTP methods for the CORS policy by reading
    /// the configuration values from the application settings. If no specific
    /// methods are configured, it allows any method.
    /// </remarks>
    private static void SetupCorsMethods(CorsPolicyBuilder builder, IConfigurationRoot appSettings)
    {
        string[]? allowedMethods = appSettings.ReadConfigurationValue<string[]>(
            $"{ParentSettingName}:Cors:AllowedMethods"
        );
        if (allowedMethods != null && allowedMethods.Length > 0)
            builder.WithMethods(allowedMethods);
        else
            builder.AllowAnyMethod();
    }

    /// <summary>
    /// Configures an HTTP client with SSL untrusted handling.
    /// </summary>
    /// <param name="services">The service collection.</param>
    /// <remarks>
    /// This method configures an HTTP client with SSL untrusted handling.
    /// It adds a named HTTP client to the service collection with the name
    /// "HttpClientWithSSLUntrusted". The client is configured to add a
    /// "Connection: Close" header to each request and to have a timeout of
    /// 5 minutes. Additionally, it sets up an HTTP handler that ignores
    /// SSL certificate errors.
    /// </remarks>
    private static void SetupHttpHandlers(IServiceCollection services) =>
        services
            .AddHttpClient(
                name: "HttpClientWithSSLUntrusted",
                configureClient: static options =>
                {
                    options.Timeout = TimeSpan.FromMinutes(5);
                    options.DefaultRequestHeaders.Accept.Add(
                        new MediaTypeWithQualityHeaderValue(
                            mediaType: GeneralConstant.API_CONTENT_TYPE
                        )
                    );
                    options.DefaultRequestHeaders.ConnectionClose = false;
                }
            )
            .ConfigurePrimaryHttpMessageHandler(
                configureHandler: static () =>
                    new HttpClientHandler()
                    {
                        ServerCertificateCustomValidationCallback = ValidateServerCertificate
                    }
            );

    /// <summary>
    /// Sets up the application with the provided WebApplicationBuilder, OpenApiInfo, SystemLogging, and IConfigurationRoot.
    /// </summary>
    /// <param name="builder">The WebApplicationBuilder.</param>
    /// <param name="oApiInfo">The OpenApiInfo.</param>
    /// <param name="logger">The SystemLogging.</param>
    /// <param name="appSettings">The IConfigurationRoot.</param>
    /// <remarks>
    /// This method builds the WebApplication and sets up the exception handler.
    /// It uses the provided configurations to initialize and configure the application.
    /// </remarks>
    private static void SetupApp(
        WebApplicationBuilder builder,
        OpenApiInfo oApiInfo,
        SystemLogging logger,
        IConfigurationRoot appSettings
    )
    {
        WebApplication app = builder.Build();
        SetupExceptionHandler(app: app, logging: logger);

        if (appSettings.ReadConfigurationValue<bool>($"{ParentSettingName}:Swagger:UI:Enabled"))
        {
            app.UseSwagger();
            app.UseSwaggerUI(options =>
            {
                options.InjectStylesheet(
                    path: $"/files/themes/theme-{appSettings.ReadConfigurationValue<string>($"{ParentSettingName}:Swagger:UI:Theme")}.css"
                );
                options.SwaggerEndpoint(
                    url: $"/swagger/{oApiInfo.Version}/swagger.json",
                    name: oApiInfo.Title
                );
            });
        }

        app.UseHttpsRedirection();
        app.UseAuthorization();
        app.MapControllers();

        app.UseStaticFiles(
            new StaticFileOptions
            {
                FileProvider = new PhysicalFileProvider(
                    Path.Combine(path1: AppContext.BaseDirectory, path2: "wwwroot")
                ),
                RequestPath = "/files"
            }
        );

        app.UseCors();
        app.Run();
    }

    /// <summary>
    /// Configures the exception handler for the application.
    /// This method is responsible for handling exceptions that occur during the execution of the application.
    /// It sets the response status code to 500 (Internal Server Error) and returns a JSON response with the error message.
    /// </summary>
    /// <param name="app">The WebApplication object.</param>
    /// <param name="logging">The SystemLogging object.</param>
    /// <remarks>
    /// This exception handler is a last resort for handling unexpected exceptions in the application.
    /// It is the responsibility of the application developers to handle exceptions properly by using try-catch blocks
    /// and logging the exceptions using the logging framework.
    /// </remarks>
    private static void SetupExceptionHandler(WebApplication app, SystemLogging logging) =>
        app.UseExceptionHandler(configure: errorApp =>
            errorApp.Run(async context =>
            {
                context.Response.StatusCode = (int)HttpStatusCode.InternalServerError;
                context.Response.ContentType = GeneralConstant.API_CONTENT_TYPE;

                IExceptionHandlerFeature? exceptionHandlerFeature =
                    context.Features.Get<IExceptionHandlerFeature>();
                if (exceptionHandlerFeature != null)
                {
                    Exception ex = exceptionHandlerFeature.Error;
                    string errorJson = JsonConvert.SerializeObject(
                        new ApiResponse()
                            .ChangeStatus(status: "System Error")
                            .ChangeMessage(
                                exception: ex,
                                logging: logging,
                                includeStackTrace: Commons.IsDevelopmentMode()
                            )
                    );

                    await context.Response.WriteAsync(text: errorJson, encoding: Encoding.UTF8);
                }
            })
        );

    /// <summary>
    /// Sets up the dependency injections for the application.
    /// This method is responsible for configuring the dependency injection container with the required services.
    /// </summary>
    /// <param name="services">The IServiceCollection object.</param>
    /// <param name="logger">The SystemLogging object.</param>
    /// <param name="appSettings">The IConfigurationRoot object.</param>
    /// <remarks>
    /// The method registers the application settings and logger as singletons and sets up various services
    /// such as language, memory cache, HTTP client, PostgreSQL, and Couchbase using the provided application settings.
    /// </remarks>
    private static void SetupDependencyInjections(
        IServiceCollection services,
        SystemLogging logger,
        IConfigurationRoot appSettings
    ) =>
        services
            .AddSingleton(implementationFactory: provider => appSettings)
            .AddSingleton(implementationFactory: provider => logger)
            .DISetupLanguage(appSettings: appSettings)
            .DISetupMemCache(appSettings: appSettings)
            .DISetupHttpClient(appSettings: appSettings)
            .DISetupPgSql(appSettings: appSettings)
            .DISetupCouchbase(appSettings: appSettings)
            .DISetupSQLite(appSettings: appSettings);
}
