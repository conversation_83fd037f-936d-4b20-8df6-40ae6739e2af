using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;

namespace idc.decision_cb.Utilities.Models.RuleSet.Input;

public partial class RuleSetConfigurationModel
{
    [JsonProperty("components")]
    public RuleSetComponent[]? Components { get; set; }

    [JsonProperty("ruleset_code")]
    [Required(ErrorMessage = "RuleSetConfigurationModel: Ruleset code is required.")]
    public required string RulesetCode { get; set; }

    [JsonProperty("ruleset_name")]
    [Required(ErrorMessage = "RuleSetConfigurationModel: Ruleset name is required.")]
    public required string RulesetName { get; set; }

    [JsonProperty("ruleset_version")]
    [Required(ErrorMessage = "RuleSetConfigurationModel: Ruleset version is required.")]
    public required string RulesetVersion { get; set; }
}
