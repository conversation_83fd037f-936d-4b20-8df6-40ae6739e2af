using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;

namespace idc.decision_cb.Utilities.Models.DecisionFlow.Input;

/// <summary>
/// Represents a decision flow edge.
/// </summary>
public class DecisionFlowEdge
{
    /// <summary>
    /// Gets or sets the edge data.
    /// </summary>
    /// <value>The edge data.</value>
    [JsonProperty(propertyName: "data", NullValueHandling = NullValueHandling.Ignore)]
    public DecisionFlowEdgeData? Data { get; set; }

    /// <summary>
    /// Gets or sets the source of the edge.
    /// </summary>
    /// <value>The source of the edge.</value>
    [JsonProperty("source"), Required(ErrorMessage = "DecisionFlowEdge: source is required."),]
    public required string Source { get; set; }

    /// <summary>
    /// Gets or sets the target of the edge.
    /// </summary>
    /// <value>The target of the edge.</value>
    [JsonProperty("target"), Required(ErrorMessage = "DecisionFlowEdge: target is required.")]
    public required string Target { get; set; }
}
