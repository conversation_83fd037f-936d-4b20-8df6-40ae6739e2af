using System.Data;
using System.Text;
using System.Text.RegularExpressions;
using idc.decision_cb.Utilities;
using idc.decision_cb.Utilities.Constants;
using idc.decision_cb.Utilities.Extensions;
using idc.decision_cb.Utilities.Helpers;
using idc.decision_cb.Utilities.Models.DecisionFlow.Input;
using idc.decision_cb.Utilities.Models.DecisionFlow.Output;
using idc.decision_cb.Utilities.Models.Enums;
using idc.decision_cb.Utilities.Models.General.Input;
using idc.decision_cb.Utilities.Models.ScoreCard.Result;
using IDX.Utilities;
using IDX.Utilities.DataProcessor;
using Microsoft.Extensions.Caching.Memory;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Npgsql;
using NpgsqlTypes;

namespace idc.decision_cb.Controllers;

public partial class ProcessController
{
    private string GetComponentConfigPG(string componentCode, DecisionComponent component)
    {
        JObject? output = Commons.GetConfigDE(iSqlite: iSqlite, docId: componentCode);
        return !string.IsNullOrWhiteSpace(value: output?.ToString())
            ? output.ToString()
            : throw new DataException(
                "not_found".ParseLanguage(
                    langToken: language,
                    replacements: [$"{component.Name} {componentCode}"]
                )
            );
    }

    private JObject? GetParamResult(string dfCode, string appNo)
    {
        var paramResult = Commons
            .GetParameterResult(docId: dfCode, iSqlite: iSqlite)
            .Replace("|", ",");

        if (string.IsNullOrWhiteSpace(value: paramResult))
            return null;

        iSqlite
            .InMemory.Connect()
            .ExecuteQuery(
                query: $"SELECT {paramResult} FROM TEMP_{appNo} WHERE cf_los_app_no = '{appNo}'",
                data: out JArray? parameters
            );

        return parameters?[index: 0x0].ToObject<JObject>();
    }

    private JObject? GetTempResults(string appNo)
    {
        iSqlite
            .InMemory.Connect()
            .ExecuteQuery(
                query: $"SELECT * FROM TEMP_{appNo} WHERE cf_los_app_no = '{appNo}'",
                data: out JArray? parameters
            );

        return JsonHelper
            .RemoveEmptyChildren(token: parameters?[index: 0x0] ?? new JObject())
            .ToObject<JObject>();
    }

    private void ExecPrerequisite(JObject dataJson)
    {
        dataJson = MandatoryAndFieldTypeValidation(inputJson: dataJson);
        string appNo = dataJson[propertyName: "cf_los_app_no"].CastToString();

        iSqlite
            .InMemory.Connect()
            .ExecuteQuery(
                query: $@"
                    {GenerateTempTableQuery(
                            masterFields: GetMasterFields(),
                            appNo: appNo
                        )}; 
                    INSERT INTO 
                        TEMP_{appNo}
                        ({string.Join(
                                separator: ",",
                                values: dataJson
                                    .Properties()
                                    .Select(selector: props => props.Name)
                                    .ToList()
                            )})
                    VALUES 
                        ({string.Join(
                                separator: ",",
                                values: dataJson
                                    .Properties()
                                    .Select(
                                        selector: props => props.Value.Type is JTokenType.Integer or JTokenType.Float
                                                ? props.Value
                                                : $"'{props.Value}'"
                                    )
                                    .ToList()
                            )});
                ",
                data: out JObject? _
            );
    }

    private JObject MandatoryAndFieldTypeValidation(JObject inputJson)
    {
        JObject cleanJson = inputJson;
        List<string> unidentifiedFields = [];
        JObject dbMf = JObject.Parse(GetMasterFieldsFromDB() ?? "{}");

        List<string> missingProperties =
        [
            .. dbMf["mandatory_fields"]!
                .ToObject<JArray>()!
                .Select(selector: prop => prop.ToString())
                .Except(
                    second: inputJson
                        .Properties()
                        .Where(predicate: prop =>
                            !string.IsNullOrWhiteSpace(prop.Value?.ToString())
                        )
                        .Select(selector: prop => prop.Name)
                ),
        ];

        if (0x0 < missingProperties.Count)
            throw new DataException(
                s: "missing_fields".ParseLanguage(
                    langToken: language,
                    replacements:
                    [
                        "mandatory",
                        string.Join(separator: ", ", values: missingProperties),
                    ]
                )
            );

        List<string> invalidType =
        [
            .. inputJson
                .Properties()
                .Where(predicate: prop =>
                {
                    string? dbMfValue = dbMf?["master_mgmt"]?[prop.Name]?.CastToString();

                    if (string.IsNullOrEmpty(value: dbMfValue))
                    {
                        unidentifiedFields.Add(item: prop.Name);
                        return false;
                    }

                    string type = dbMfValue.Contains(value: '_')
                        ? dbMfValue.Split(separator: "_")[0x0]
                        : dbMfValue;

                    return type switch
                    {
                        "t" => prop.Value.Type is not JTokenType.String,
                        "i" or "r" => prop.Value.Type
                            is not (JTokenType.Integer or JTokenType.Float),
                        _ => false,
                    };
                })
                .Select(selector: prop => prop.Name),
        ];

        if (invalidType.Count > 0)
            throw new DataException(
                s: "missmatch_fields".ParseLanguage(
                    langToken: language,
                    replacements: ["type", string.Join(", ", invalidType)]
                )
            );

        return unidentifiedFields.Aggregate(
            seed: cleanJson,
            func: (current, key) =>
            {
                current.Remove(key);
                return current;
            }
        );
    }

    private async Task<DecisionFlowResult> GetResultComponent(
        ExecuteComponentInputModel inputJson,
        bool isSimulation = false,
        DecisionFlowConfigurationInputModel? dfSimulate = null
    )
    {
        ScorecardCharacteristics = [];

        DecisionComponent component = isSimulation switch
        {
            true => DecisionComponent.DecisionFlow,
            _ => DecisionComponent.GetByCode(inputJson.Code),
        };

        JObject joConfig = dfSimulate is not null
            ? JsonConvert
                .SerializeObject(dfSimulate.GenerateCouchbaseOutput(pgHelper: pgHelper))
                .ToJObject()
            : GetComponentConfigPG(componentCode: inputJson.Code, component: component).ToJObject();

        DecisionFlowResult joResult = new() { Code = joConfig["df_code"].CastToString() };

        if (component.Name == DecisionComponent.DF_NAME)
        {
            DecisionFlowConfigurationOutputModel dfConfig =
                JsonConvert.DeserializeObject<DecisionFlowConfigurationOutputModel>(
                    joConfig.CastToString()
                ) ?? throw new DataException("Invalid Decision Flow Configuration");

            joResult.Stages = await ProcessDF(inputJson, dfConfig, isSimulation);
            joResult.Timestamp = DateTime.Now.ToString(GeneralConstant.DEFVAL_DATETIME_FMT_YMDHMSF);

            if (!isSimulation)
                FinalProcessDF(appNo: inputJson.AppNo, dfResponse: joResult);
            else
                joResult.Detail = SimulationDetailResponse(joResult.Stages);
        }

        return joResult;
    }

    private DecisionFlowResultStage GenerateDecisionResult(
        string? cbQry,
        string code,
        string stepName
    )
    {
        DecisionFlowResultStage joResult = new() { Stage = stepName, Components = code };

        List<DecisionFlowResultDecision> jaDec = [];
        try
        {
            if (!string.IsNullOrWhiteSpace(cbQry))
            {
                iSqlite
                    .InMemory.Connect()
                    .ExecuteQuery(query: cbQry, data: out JObject? sqLiteOutputs);

                if (sqLiteOutputs != null)
                {
                    JToken? sqLiteOutput = JArray
                        .Parse(sqLiteOutputs[resultsStr].CastToString())
                        .FirstOrDefault();

                    if (sqLiteOutput != null)
                    {
                        JObject joOutput = sqLiteOutput.CastToString().ToJObject();

                        foreach (JProperty property in joOutput.Properties())
                        {
                            jaDec.Add(
                                new DecisionFlowResultDecision
                                {
                                    FieldName = property.Name,
                                    FieldValue = property.Value,
                                }
                            );
                        }
                        joResult.Result = jaDec;
                    }
                }
            }
            else
            {
                joResult.Result = jaDec;
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error in exec {code}: {ex.Message}");
            joResult.Result = jaDec;
        }

        joResult.Timestamp = DateTime.Now.ToString(GeneralConstant.DEFVAL_DATETIME_FMT_YMDHMSF);
        return joResult;
    }

    private static JObject SimulationDetailResponse(List<DecisionFlowResultStage> jaStages)
    {
        JObject joDetail = [];
        try
        {
            foreach (DecisionFlowResultStage stage in jaStages)
            {
                string[] componentArr = stage.Components.Split(',');
                foreach (string component in componentArr)
                {
                    bool isDecisionFlow =
                        component != "start"
                        && DecisionComponent.GetByCodePrecise(component)
                            == DecisionComponent.DecisionFlow;

                    if (isDecisionFlow)
                    {
                        ConvertSimulationDfResponse(
                            joDetail: joDetail,
                            stageResult: stage.Result,
                            component: component
                        );
                    }
                    else
                    {
                        List<DecisionFlowResultDecision> componentValues =
                            stage.Result as List<DecisionFlowResultDecision> ?? [];
                        string detailValue = string.Join(
                            separator: ", ",
                            values: componentValues.Select(selector: static cv =>
                                $"{cv.FieldName} = {cv.FieldValue}"
                            )
                        );

                        if (joDetail.ContainsKey(component))
                        {
                            joDetail[component] = string.Join(
                                ", ",
                                joDetail[component].CastToString(),
                                detailValue
                            );
                        }
                        else
                        {
                            joDetail.Add(propertyName: component, value: detailValue);
                        }
                    }
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Err SimulationResponse: {ex.Message}");
        }

        return joDetail;
    }

    private static void ConvertSimulationDfResponse(
        JObject joDetail,
        dynamic stageResult,
        string component
    )
    {
        List<DecisionFlowResultStage> resultValues =
            stageResult as List<DecisionFlowResultStage> ?? [];
        if (joDetail.ContainsKey(component))
            joDetail[component] = SimulationDetailResponse(resultValues);
        else
            joDetail.Add(component, SimulationDetailResponse(resultValues));
    }

    private async Task<List<DecisionFlowResultStage>> ProcessDF(
        ExecuteComponentInputModel inputJson,
        DecisionFlowConfigurationOutputModel joConfig,
        bool isSimulation
    )
    {
        List<DecisionFlowStep>? steps = joConfig.DFSteps;
        List<DecisionFlowProcess>? processSteps = joConfig.DFProcess;

        string nextStep = "step 1";
        string label = GeneralConstant.DEFVAL_STR_ELLIPSIS;
        int stepCounter = 0;

        string? cbQry = null;
        List<DecisionFlowResultStage> jaComponentResults = [];

        string[] commonComponent = ["RLX", "DBX", "DTX", "SCX", "CRX", "QUE"];
        string[] integrationComponent = ["FBE", "MLE", "XTR", "CBX", "CCX"];

        DecisionFlowResultStage joComponentResult = new()
        {
            Stage = stepCounter.ToString(),
            Components = "start",
            Result = new JArray(),
            Timestamp = DateTime.Now.ToString(GeneralConstant.DEFVAL_DATETIME_FMT_YMDHMSF),
        };
        jaComponentResults.Add(joComponentResult);

        while (true)
        {
            stepCounter++;
            DecisionFlowProcess? processStep = processSteps?.Find(match: dfProc =>
                string.Compare(dfProc.Source, nextStep, true) == 0
                && string.Compare(dfProc.Label, label, true) == 0
            );

            if (processStep != null)
                nextStep = processStep.Target;
            else
                break;

            DecisionFlowStep step =
                steps?.Find(match: dfStep => dfStep.Step == nextStep)
                ?? throw new DataException(
                    "Error: "
                        + GeneralConstant.API_RESP_NOT_FOUND.ParseLanguage(
                            language,
                            [$"Step {nextStep}"]
                        )
                );

            string stepComponent = step.Components.ToUpper();
            string prefixComponen = stepComponent[..3];

            if (commonComponent.Contains(prefixComponen))
            {
                ProcessSccCharacteristics(
                    chars: step.ScorecardCharacteristics,
                    appNo: inputJson.AppNo
                );

                cbQry = step
                    .NoSql?.CastToString()
                    .Replace(oldValue: "{{IdTempTable}}", newValue: inputJson.AppNo);

                joComponentResult = GenerateDecisionResult(
                    cbQry: cbQry,
                    code: stepComponent,
                    stepName: stepCounter.ToString()
                );

                label = SetNextComponentLabel(
                    joComponentResult: joComponentResult.Result,
                    prefixComponen: prefixComponen
                );
            }
            else if (integrationComponent.Contains(prefixComponen))
            {
                joComponentResult = ProcessIntegrationComponent(
                    appNo: inputJson.AppNo,
                    componentCode: stepComponent,
                    stepName: stepCounter.ToString(),
                    isSimulation: isSimulation
                );
                label = SetNextComponentLabel(
                    joComponentResult: joComponentResult.Result,
                    prefixComponen: prefixComponen
                );
            }
            else
            {
                ExecuteComponentInputModel dfInput = new()
                {
                    AppNo = inputJson.AppNo,
                    Code = stepComponent,
                };

                DecisionFlowConfigurationOutputModel dfConfig =
                    JsonConvert.DeserializeObject<DecisionFlowConfigurationOutputModel>(
                        GetComponentConfigPG(
                            componentCode: stepComponent,
                            component: DecisionComponent.DecisionFlow
                        )
                    ) ?? throw new DataException("Error: Invalid Decision Flow Configuration");

                joComponentResult = new()
                {
                    Stage = stepCounter.ToString(),
                    Components = stepComponent,
                    Result = await ProcessDF(dfInput, dfConfig, isSimulation),
                    Timestamp = DateTime.Now.ToString(GeneralConstant.DEFVAL_DATETIME_FMT_YMDHMSF),
                };

                label = GeneralConstant.DEFVAL_STR_ELLIPSIS;
            }

            jaComponentResults.Add(joComponentResult);

            if (!IsValidProcessResult(joComponentResult))
                break;
        }

        return jaComponentResults;
    }

    private static bool IsValidProcessResult(dynamic joComponentResult) =>
        (
            joComponentResult.Result is List<DecisionFlowResultDecision> jaDecisions
            && jaDecisions.Count > 0
        )
        || (
            joComponentResult.Result is List<DecisionFlowResultStage> jaStages
            && (
                jaStages[^1].Components == "start"
                || jaStages[^1].Result is List<DecisionFlowResultDecision> jaDFDecisions
                    && jaDFDecisions.Count > 0
            )
        );

    private List<ScorecardCharacteristicsResultModel> ScorecardCharacteristics = [];

    private void ProcessSccCharacteristics(JObject chars, string appNo)
    {
        if (chars.HasValues)
        {
            foreach (JProperty prop in chars.Properties())
            {
                string sccName = prop.Name;
                ScorecardCharacteristics.Add(
                    new ScorecardCharacteristicsResultModel()
                    {
                        Name = "0",
                        SccName = sccName,
                        CustValue = null,
                        Score = 0,
                    }
                );

                JObject sccChars = JObject.Parse(prop.Value.ToString());
                foreach (JProperty sccChar in sccChars.Properties())
                {
                    string sccCharQuery = Regex.Replace(
                        sccChar.Value.CastToString(),
                        @"\s*\*\s*\d+(\.\d+)?$",
                        string.Empty
                    );

                    string sccCharName = sccChar.Name;
                    string query =
                        @$"SELECT {sccCharName}, {sccCharQuery} as score 
                        FROM TEMP_{appNo} where cf_los_app_no = '{appNo}'";

                    List<object?> charResultsSqlite = [];

                    iSqlite
                        .InMemory.Connect()
                        .ExecuteQuery(query: query, data: out JObject? sqLiteOutputs);

                    if (sqLiteOutputs != null)
                    {
                        JToken? sqLiteOutput = JArray
                            .Parse(sqLiteOutputs[resultsStr].CastToString())
                            .FirstOrDefault();

                        JObject joOutput = sqLiteOutput.CastToString().ToJObject();
                        foreach (JProperty property in joOutput.Properties())
                        {
                            charResultsSqlite.Add(property.Value);
                        }
                    }

                    ScorecardCharacteristicsResultModel sccResult = new()
                    {
                        Name = sccCharName,
                        SccName = sccName,
                        CustValue = charResultsSqlite[0],
                        Score = charResultsSqlite[1],
                    };

                    ScorecardCharacteristics.Add(sccResult);
                }
            }
        }
    }

    private DecisionFlowResultStage ProcessIntegrationComponent(
        string appNo,
        string componentCode,
        string stepName,
        bool isSimulation
    )
    {
        DecisionFlowResultStage joResult = new()
        {
            Stage = stepName,
            Components = componentCode,
            Result = new JArray(),
        };
        List<DecisionFlowResultDecision> jaDec = [];

        iSqlite
            .InMemory.Connect()
            .ExecuteQuery(
                $"SELECT * FROM TEMP_{appNo} where cf_los_app_no = '{appNo}'",
                out JObject? sqLiteOutputs
            );

        if (sqLiteOutputs != null)
        {
            try
            {
                JToken? sqLiteOutput = sqLiteOutputs[resultsStr]
                    .CastToString()
                    .ToJArray()
                    .FirstOrDefault();

                if (sqLiteOutput != null)
                {
                    JToken cleanTempData = JsonHelper.RemoveEmptyChildren(sqLiteOutput);
                    JObject joReq;
                    string urlLib;
                    string url;

                    if (componentCode.StartsWith("CCX", StringComparison.CurrentCultureIgnoreCase))
                    {
                        urlLib =
                            GetSetCache(
                                key: "champChallengeAPI",
                                defaultValue: () =>
                                    Commons.ReadConfigurationValue<string>(
                                        configuration,
                                        "APISettings:urlAPI_idcservice"
                                    )
                            ) ?? string.Empty;

                        url = $"{urlLib}ChampionChallenger/ProcessCC";

                        joReq = new()
                        {
                            { "id", componentCode },
                            { "table_name", appNo },
                            { "is_simulation", isSimulation.CastToString().ToLower() },
                        };
                    }
                    else
                    {
                        urlLib =
                            GetSetCache(
                                key: "integrationAPI",
                                defaultValue: () =>
                                    Commons.ReadConfigurationValue<string>(
                                        configuration,
                                        "APISettings:urlAPI_idclibrary"
                                    )
                            ) ?? string.Empty;

                        url = $"{urlLib}Integration/Process";

                        joReq = new()
                        {
                            { "code", componentCode },
                            { "appno", appNo },
                            { appNo, cleanTempData },
                            { "source_process", "decflow" },
                        };
                    }

                    StringContent contentData = new(
                        JsonConvert.SerializeObject(joReq),
                        Encoding.UTF8,
                        "application/json"
                    );

                    HttpResponseMessage response = httpClient
                        .PostAsync(requestUri: url, content: contentData)
                        .Result;
                    string integrationResponse = response.Content.ReadAsStringAsync().Result;
                    JObject joResp = JObject.Parse(integrationResponse);
                    ConvertIntegrationResponse(appNo, jaDec, joResp, componentCode);
                }
            }
            catch (Exception ex)
            {
                jaDec.Add(
                    new DecisionFlowResultDecision
                    {
                        FieldName = resultStr,
                        FieldValue = ex.Message,
                    }
                );
            }
            finally
            {
                joResult.Result = jaDec;
            }
        }
        joResult.Timestamp = DateTime.Now.ToString(GeneralConstant.DEFVAL_DATETIME_FMT_YMDHMSF);
        return joResult;
    }

    private void ConvertIntegrationResponse(
        string appNo,
        List<DecisionFlowResultDecision> jaDec,
        JObject joResp,
        string componentCode
    )
    {
        if (
            joResp.TryGetValue(
                propertyName: "data",
                comparison: StringComparison.CurrentCultureIgnoreCase,
                value: out JToken? jData
            )
        )
        {
            JObject joData = JObject.Parse(jData.CastToString());

            if (componentCode.StartsWith("CCX", StringComparison.CurrentCultureIgnoreCase))
            {
                var ccxField = joData.GetValue<string>("field_name") ?? string.Empty;
                var ccxValue = joData.GetValue<string>("field_value");

                if (ccxField != string.Empty)
                {
                    iSqlite
                        .InMemory.Connect()
                        .ExecuteScalar(
                            @$"UPDATE TEMP_{appNo} SET {ccxField} = {(ccxValue is null ? "NULL" : $"'{ccxValue}'")} 
                                WHERE cf_los_app_no = '{appNo}' RETURNING {ccxField}",
                            out object? _
                        );

                    jaDec.Add(
                        new DecisionFlowResultDecision
                        {
                            FieldName = ccxField,
                            FieldValue = ccxValue ?? "null",
                        }
                    );
                }
            }

            if (
                joData.TryGetValue(
                    appNo,
                    StringComparison.CurrentCultureIgnoreCase,
                    out JToken? joTempData
                )
            )
                UpdateTempBasedIntegrationResponse(tempResp: joTempData, appNo: appNo);

            if (
                joData.TryGetValue(
                    resultStr,
                    StringComparison.CurrentCultureIgnoreCase,
                    out JToken? integrationResp
                )
            )
                jaDec.Add(
                    new DecisionFlowResultDecision
                    {
                        FieldName = resultStr,
                        FieldValue = JsonConvert.SerializeObject(integrationResp),
                    }
                );
        }
        else
        {
            jaDec.Add(
                new DecisionFlowResultDecision { FieldName = resultStr, FieldValue = joResp }
            );
        }
    }

    private void UpdateTempBasedIntegrationResponse(JToken tempResp, string appNo)
    {
        if (
            tempResp != null
            && tempResp.HasValues
            && JsonHelper.RemoveEmptyChildren(tempResp) is JObject cleanTempResp
        )
        {
            List<string> setClauses =
            [
                .. cleanTempResp
                    .Properties()
                    .Where(p => p.Value.Type is not JTokenType.Null && p.Name != "cf_los_app_no")
                    .Select(p => $"{p.Name} = '{p.Value}'"),
            ];

            iSqlite
                .InMemory.Connect()
                .ExecuteQuery(
                    $"UPDATE TEMP_{appNo} SET {string.Join(", ", setClauses)} WHERE cf_los_app_no = '{appNo}'",
                    out JObject? _
                );
        }
    }

    private static string SetNextComponentLabel(dynamic joComponentResult, string prefixComponen) =>
        prefixComponen switch
        {
            "QUE" or "CCX" => GetLabelConditionRule(joComponentResult),
            _ => GeneralConstant.DEFVAL_STR_ELLIPSIS,
        };

    private static string GetLabelConditionRule(dynamic joComponentResult) =>
        joComponentResult is List<DecisionFlowResultDecision> jaDecisions
            ? (string)(
                jaDecisions.FirstOrDefault()?.FieldValue ?? GeneralConstant.DEFVAL_STR_ELLIPSIS
            )
            : GeneralConstant.DEFVAL_STR_ELLIPSIS;

    private void FinalProcessDF(string appNo, DecisionFlowResult dfResponse)
    {
        iSqlite
            .InMemory.Connect()
            .ExecuteQuery(
                query: $"select * from TEMP_{appNo} where cf_los_app_no = '{appNo}';",
                data: out JObject? sqLiteOutput
            );

        JObject resp = JObject.Parse(sqLiteOutput?[resultsStr]?[0]?.CastToString() ?? "{}");
        resp.Add(propertyName: "date_start", value: dateStart);
        resp.Add(
            propertyName: "date_end",
            value: DateTime.Now.ToString(GeneralConstant.DEFVAL_DATETIME_FMT_YMDHMSF)
        );

        Task.Run(() =>
        {
            try
            {
                UpsertResultProcessDf(
                    appNo,
                    resp.ToString(),
                    JsonConvert.SerializeObject(dfResponse)
                );
            }
            catch (Exception ex)
            {
                logging.WriteLog(ex, LogLevel.Error);
            }
        });
    }

    private void UpsertResultProcessDf(string appNo, string data, string apiResponse)
    {
        // TODO: Need fix pgHelper for handle async concurrent hit
        string? pass = GetSetCache(
            key: "passDB",
            defaultValue: () =>
                Commons.DecryptText(
                    Commons.ReadConfigurationValue<string>(configuration, "configPass:passwordDB")
                        ?? string.Empty
                )
        );

        string conString = GetSetCache(
            key: "connString_en",
            defaultValue: () =>
                Commons
                    .ReadConfigurationValue<string>(
                        configuration,
                        "DbContextSettings:ConnectionString_en"
                    )!
                    .Replace("{pass}", pass)
        );

        using NpgsqlConnection conn = new(conString);
        conn.Open();

        using NpgsqlCommand cmd = conn.CreateCommand();
        {
            cmd.CommandText =
                "SELECT * FROM inmemory_de.result_de_upsert(@p_name_id, @p_value, @p_result, @p_chars)";
            cmd.CommandType = CommandType.Text;
            cmd.CommandTimeout = 60;

            cmd.Parameters.AddWithValue("p_name_id", NpgsqlDbType.Varchar, appNo);
            cmd.Parameters.AddWithValue("p_value", NpgsqlDbType.Text, data);
            cmd.Parameters.AddWithValue("p_result", NpgsqlDbType.Text, apiResponse);
            cmd.Parameters.AddWithValue(
                "p_chars",
                NpgsqlDbType.Text,
                JsonConvert.SerializeObject(ScorecardCharacteristics)
            );
        }

        _ = cmd.ExecuteScalar();
        conn.Close();
    }

    private void UpsertResultProcessDFWithDI(
        string appNo,
        string data,
        string apiResponse,
        PgSqlHelper pgh
    )
    {
        pgh.Connect(useTransaction: true)
            .ExecuteNonQuery(
                result: out int _,
                spCallInfo: new()
                {
                    Schema = "inmemory_de",
                    SPName = "result_de_upsert",
                    Parameters =
                    [
                        new()
                        {
                            Name = "p_name_id",
                            Value = appNo,
                            DataType = NpgsqlDbType.Text,
                        },
                        new()
                        {
                            Name = "p_value",
                            Value = data,
                            DataType = NpgsqlDbType.Text,
                        },
                        new()
                        {
                            Name = "p_result",
                            Value = apiResponse,
                            DataType = NpgsqlDbType.Text,
                        },
                        new()
                        {
                            Name = "p_chars",
                            Value = JsonConvert.SerializeObject(ScorecardCharacteristics),
                            DataType = NpgsqlDbType.Text,
                        },
                    ],
                }
            )
            .Commit(restartTransaction: true);
    }

    private static readonly SemaphoreSlim semaphore = new(1, 1);

    private static async Task ForceQueueProcess(Func<Task> operation)
    {
        Random random = new();
        int randomDelay = random.Next(minValue: 1, maxValue: 1000);

        await Task.Delay(TimeSpan.FromMilliseconds(randomDelay));

        await semaphore.WaitAsync();
        try
        {
            await operation();
        }
        finally
        {
            semaphore.Release();
        }
    }

    private T GetSetCache<T>(string key, Func<T> defaultValue)
    {
        T? _cache = cache.Get<T>(key);
        if (EqualityComparer<T>.Default.Equals(x: _cache, y: default(T)))
        {
            T? val = defaultValue();
            cache.Add(key, val, CacheItemPriority.Normal);
            return val;
        }
        return _cache;
    }
}
