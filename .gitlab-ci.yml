stages:
  - build
  - deploy
  - security-check
  - security-report
  - tagging
  
variables:
  IMAGE_KUBECTL: hub.idecision.ai:8443/production/kubectl:latest
  IMAGE_RELEASE: hub.idecision.ai:8443/release/idecision8-$CI_PROJECT_NAME:release
  REGISTRY_USER: $CI_REGISTRY_USER
  REGISTRY_PASSWORD: $CI_REGISTRY_PASSWORD
  REGISTRY_URL: $CI_REGISTRY_URL
  SONARQUBE_TOKEN: $CI_SONARQUBE_TOKEN
  SONARQUBE_URL: $CI_SONARQUBE_URL
  GITLAB_USER: $CI_GITLAB_USER
  GITLAB_TOKEN: $CI_GITLAB_TOKEN
  IDC_SERVICE: ""

.docker_build_template: &docker_build_template
  services:
    - name: hub.idecision.ai:8443/production/docker:20.10-dind
      alias: docker
  variables:
    DOCKER_HOST: tcp://docker:2375
    DOCKER_DRIVER: overlay2
    DOCKER_TLS_CERTDIR: ""
  before_script:
    - echo "$CI_COMMIT_BRANCH"
    - echo "$REGISTRY_PASSWORD" | docker login --username "$REGISTRY_USER" "$REGISTRY_URL" --password-stdin
  script:
    - |
      # Trim whitespace from CI_COMMIT_BRANCH
      BRANCH=$(echo "$CI_COMMIT_BRANCH" | xargs)
      echo "Normalized Branch: $BRANCH"
      
      if [ "$BRANCH" == "release_dev" ]; then
          echo "Enter Condition $CI_COMMIT_BRANCH"
          source Deployment
          export $(cut -d= -f1 Deployment)
          sed -i "s/LABEL version=\"v1.0.0\"/LABEL version=\"$VERSION\"/g" Dockerfile
          docker build -t hub.idecision.ai:8443/development/idecision8-$CI_PROJECT_NAME:latest .
          docker push hub.idecision.ai:8443/development/idecision8-$CI_PROJECT_NAME:latest
      elif [ "$BRANCH" == "sit" ]; then
          echo "Enter Condition $CI_COMMIT_BRANCH"
          source Deployment
          export $(cut -d= -f1 Deployment)
          sed -i "s/LABEL version=\"v1.0.0\"/LABEL version=\"$VERSION\"/g" Dockerfile
          docker build -t hub.idecision.ai:8443/sit/idecision8-$CI_PROJECT_NAME:latest .
          docker push hub.idecision.ai:8443/sit/idecision8-$CI_PROJECT_NAME:latest
      elif [ "$BRANCH" == "prod" ]; then
          echo "Enter Condition $CI_COMMIT_BRANCH"
          source Deployment
          export $(cut -d= -f1 Deployment)
          sed -i "s/LABEL version=\"v1.0.0\"/LABEL version=\"$VERSION\"/g" Dockerfile
          docker build -t hub.idecision.ai:8443/production/idecision8-$CI_PROJECT_NAME:latest .
          docker push hub.idecision.ai:8443/production/idecision8-$CI_PROJECT_NAME:latest
      else
          echo "Branch $BRANCH does not match any deployment condition."
          exit 1
      fi

.docker_tagging_template: &docker_tagging_template
  services:
    - name: hub.idecision.ai:8443/production/docker:20.10-dind
      alias: docker
  variables:
    DOCKER_HOST: tcp://docker:2375
    DOCKER_DRIVER: overlay2
    DOCKER_TLS_CERTDIR: ""
  before_script:
    - apk update && apk add git
    - echo "$REGISTRY_PASSWORD" | docker login --username "$REGISTRY_USER" "$REGISTRY_URL" --password-stdin
  script:
    - git config --global user.email "<EMAIL>"
    - git config --global user.name "script"
    - git clone https://${CI_GITLAB_USER}:${CI_GITLAB_TOKEN}@scm.idecision.ai/idecision_source_net8/$CI_PROJECT_NAME.git
    - cd $CI_PROJECT_NAME && git checkout prod
    - source Deployment
    - export $(cut -d= -f1 Deployment)
    - echo $VERSION
    - git tag v$VERSION
    - git push --tags
    - docker pull hub.idecision.ai:8443/production/idecision8-$CI_PROJECT_NAME:latest
    - docker tag hub.idecision.ai:8443/production/idecision8-$CI_PROJECT_NAME:latest hub.idecision.ai:8443/release/idecision8-$CI_PROJECT_NAME:latest
    - docker push hub.idecision.ai:8443/release/idecision8-$CI_PROJECT_NAME:latest

.deploy_template: &deploy_template
  image:
    name: $IMAGE_KUBECTL
    entrypoint: [""]
  variables:
    GIT_STRATEGY: none
  before_script:
    - echo "$CI_COMMIT_BRANCH"
    - mkdir -p $HOME/.kube
  script:
    # Menentukan kubeconfig berdasarkan branch
    - |
      # Trim whitespace dari CI_COMMIT_BRANCH
      BRANCH=$(echo "$CI_COMMIT_BRANCH" | xargs)
      echo "Normalized Branch: $BRANCH"
      
      if [ "$BRANCH" == "release_dev" ]; then
        echo "Enter Condition $CI_COMMIT_BRANCH"
        echo "$CI_KUBECONFIG_CLUSTER_DEV" | base64 -d > $HOME/.kube/config
        NAMESPACE="dev"
        IMAGE_REGISTRY="hub.idecision.ai:8443/development"
      elif [ "$BRANCH" == "sit" ]; then
        echo "Enter Condition $CI_COMMIT_BRANCH"
        echo "$CI_KUBECONFIG_CLUSTER_SIT" | base64 -d > $HOME/.kube/config
        NAMESPACE="sit"
        IMAGE_REGISTRY="hub.idecision.ai:8443/sit"
      elif [ "$BRANCH" == "prod" ]; then
        echo "Enter Condition $CI_COMMIT_BRANCH"
        echo "$CI_KUBECONFIG_CLUSTER_PRD" | base64 -d > $HOME/.kube/config
        NAMESPACE="prod"
        IMAGE_REGISTRY="hub.idecision.ai:8443/production"
      else
        echo "Branch $BRANCH does not match any deployment condition."
        exit 1
      fi
    # Menampilkan informasi node
    - kubectl get node -o wide
    # Mengatur nama image
    - CI_PROJECT_NAME=$(echo "$CI_PROJECT_NAME" | sed 's/\./-/g')
    - CI_PROJECT_NAME_IMG=$(echo "$CI_PROJECT_NAME" | sed 's/\-/./g')
    # Update dan restart deployment
    - kubectl -n "$NAMESPACE" set image deployment/"$CI_PROJECT_NAME-dep" "$CI_PROJECT_NAME-dep"="$IMAGE_REGISTRY/idecision8-$CI_PROJECT_NAME_IMG:latest"
    - kubectl -n "$NAMESPACE" rollout restart deployment/"$CI_PROJECT_NAME-dep"

.trivy_template: &trivy_template
  services:
    - name: hub.idecision.ai:8443/production/trivy-security-generate-report:latest
      alias: docker
  variables:
    DOCKER_HOST: tcp://docker:2375
    DOCKER_DRIVER: overlay2
    DOCKER_TLS_CERTDIR: ""
    GIT_STRATEGY: none
  cache:
    paths:
      - .trivycache/
  before_script:
    - echo "$REGISTRY_PASSWORD" | docker login --username "$REGISTRY_USER" "$REGISTRY_URL" --password-stdin

  script:
    - docker pull hub.idecision.ai:8443/development/idecision8-$CI_PROJECT_NAME:latest
    - docker tag hub.idecision.ai:8443/development/idecision8-$CI_PROJECT_NAME:latest idecision8-$CI_PROJECT_NAME:latest
    - trivy --version
    - trivy clean --scan-cache
    - trivy image --download-db-only --no-progress --cache-dir .trivycache/
    - trivy image --exit-code 0 --cache-dir .trivycache/ --no-progress --format template --template  "@/contrib/html.tpl" -o "$CI_PROJECT_NAME-scanning-report.html" idecision8-$CI_PROJECT_NAME:latest
    - trivy image --exit-code 0 --cache-dir .trivycache/ --no-progress --severity HIGH idecision8-$CI_PROJECT_NAME:latest
    - trivy image --exit-code 1 --cache-dir .trivycache/ --severity CRITICAL --no-progress idecision8-$CI_PROJECT_NAME:latest

.sonarqube_template: &sonarqube_template
  image: hub.idecision.ai:8443/production/dotnet-sonarqube-report:latest
  variables:
    SONAR_USER_HOME: "${CI_PROJECT_DIR}/.sonar"
    GIT_DEPTH: "0"
  cache:
    key: "${CI_JOB_NAME}"
    paths:
      - .sonar/cache
      -  apt-cache/
  before_script:
    - "apt-get update"
    - "apt-get install --yes --no-install-recommends openjdk-17-jre"

build_image:
  stage: build
  <<: *docker_build_template
  when: always
  allow_failure: false
  only:
    - release_dev
    - sit
    - prod

deploy:
  stage: deploy
  needs: [build_image] 
  <<: *deploy_template
  allow_failure: false
  only:
    - release_dev
    - sit
    - prod

tagging_release:
  stage: tagging
  extends: .docker_tagging_template
  when: manual
  allow_failure: false
  only:
    - prod

trivy_check:
  stage: security-check
  needs: [deploy] 
  <<: *trivy_template
  allow_failure: false
  artifacts:
    paths:
    - $CI_PROJECT_NAME-scanning-report.html
    expire_in: 1 week
  when: always
  only:
    - release_dev

sonarqube_check:
  stage: security-check
  extends: .sonarqube_template
  needs: [deploy] 
  script: 
      - dotnet tool install --global dotnet-sonarscanner
      - export PATH=\"$PATH:$HOME/.dotnet/tools\"
      - dotnet sonarscanner begin /k:"gitlab-$CI_PROJECT_NAME" /d:sonar.host.url="$CI_SONARQUBE_URL" /d:sonar.token="$SONARQUBE_TOKEN" /d:sonar.scanner.skipCache=true
      - rm -rf file script .gitlab-ci.yaml Dockerfile appsettings.json .gitlab-ci.yml-backup Deployment README.md log logs Logs Log files File Files template Data
      - dotnet build
      - dotnet sonarscanner end /d:sonar.token="$SONARQUBE_TOKEN"
  allow_failure: false
  only:
    - release_dev

sonarqube_generate_report:
  stage: security-report
  image: hub.idecision.ai:8443/production/dotnet-sonarqube-generate-report:latest
  needs: [sonarqube_check] 
  script:
    - >
      java -jar /opt/sonarqube-idecision/sonar-cnes-report.jar 
      -t $SONARQUBE_TOKEN 
      -s $CI_SONARQUBE_URL 
      -fem -r /opt/sonarqube-idecision/code-analysis-template.docx 
      -p gitlab-$CI_PROJECT_NAME -o reports/internal
    - >
      java -jar /opt/sonarqube-idecision/sonar-cnes-report.jar 
      -t $SONARQUBE_TOKEN 
      -s $CI_SONARQUBE_URL 
      -fem -r /opt/sonarqube-idecision/template-idecision-sonarqube-client.docx
      -p gitlab-$CI_PROJECT_NAME -o reports/client
  artifacts:
    expire_in: 1 week
    paths:
      - reports/*
  allow_failure: false
  only:
    - release_dev
