using System.Text.RegularExpressions;
using idc.decision_cb.Utilities.Models.DecisionFlow.Output;
using IDX.Utilities.DataProcessor;
using Newtonsoft.Json.Linq;

namespace idc.decision_cb.Utilities.Models.DecisionFlow.Input;

/// <summary>
/// Represents a decision flow configuration input model.
/// </summary>
/// <example>
/// {
///     "df_header": {
///         "flows_id": "1",
///         "name": "Decision Flow 1",
///         "description": "This is the first decision flow."
///     },
///     "df_nodes": [
///         {
///             "node_id": "1",
///             "node_name": "Node 1",
///             "node_type": "start",
///             "node_next": "2"
///         },
///         {
///             "node_id": "2",
///             "node_name": "Node 2",
///             "node_type": "end",
///             "node_next": "3"
///         },
///         {
///             "node_id": "3",
///             "node_name": "Node 3",
///             "node_type": "end",
///             "node_next": null
///         }
///     ],
///     "df_edges": [
///         {
///             "edge_id": "1",
///             "edge_source": "1",
///             "edge_target": "2",
///             "edge_label": "Edge 1"
///         },
///         {
///             "edge_id": "2",
///             "edge_source": "2",
///             "edge_target": "3",
///             "edge_label": "Edge 2"
///         }
///     ]
/// }
/// </example>
public partial class DecisionFlowConfigurationInputModel
{
    /// <summary>
    /// Version of the decision flow.
    /// </summary>
    private string DFVersion { get; set; } = "24.0001";

    /// <summary>
    /// List of decision flow processes.
    /// </summary>
    private List<DecisionFlowProcess>? Process { get; set; } = [];

    /// <summary>
    /// List of decision flow steps.
    /// </summary>
    private List<DecisionFlowStep>? Steps { get; set; } = [];

    private List<JObject>? Queries { get; set; } = [];

    /// <summary>
    /// Generates the Couchbase output based on the current decision flow configuration.
    /// </summary>
    /// <returns>
    /// A <see cref="DecisionFlowConfigurationOutputModel"/> containing the decision flow code, name, version, steps, and process.
    /// </returns>
    public DecisionFlowConfigurationOutputModel GenerateCouchbaseOutput(PgSqlHelper pgHelper)
    {
        GenerateStepsAndProcess();
        CleanserDFProcess();
        QueryCollector(pgHelper: pgHelper);

        return new DecisionFlowConfigurationOutputModel
        {
            DFCode = $"D{Header!.FlowsId}",
            DFName = Header.Name,
            DFVersion = DFVersion ?? $"{DateTime.Now:yy}.0001",
            DFSteps = Steps,
            DFProcess = Process,
        };
    }

    /// <summary>
    /// Regex pattern to find numeric values.
    /// </summary>
    [GeneratedRegex(@"\d+")]
    private static partial Regex RegexNumericFinder();

    /// <summary>
    /// Strips the given step name by taking the specified position
    /// if the step name contains the given separator.
    /// </summary>
    /// <param name="stepName">The step name to strip.</param>
    /// <param name="takePos">The position to take from the step name.</param>
    /// <param name="defValue">The default value to return if the step name does not contain the separator.</param>
    /// <returns>The stripped step name.</returns>
    private static string StepNameStripper(
        string stepName,
        int takePos = 0,
        string? defValue = null
    ) =>
        stepName.Contains(value: " - ")
            ? stepName.Split(separator: " - ")[takePos]
            : (defValue ?? stepName);

    /// <summary>
    /// Cleans the decision flow process by removing duplicates and stripping unnecessary
    /// characters from the source, target, and label.
    /// </summary>
    private void CleanserDFProcess()
    {
        if (Process is null || Process.Count == 0x0)
            return;

        Process =
        [
            .. Process
                .Select(selector: static process => new DecisionFlowProcess
                {
                    Source = StepNameStripper(stepName: process.Source),
                    Target = StepNameStripper(stepName: process.Target),
                    Label = StepNameStripper(stepName: process.Label ?? "..."),
                })
                .DistinctBy(keySelector: static process => (process.Source, process.Target)),
        ];
    }

    /// <summary>
    /// Removes duplicate steps from the steps list.
    /// </summary>
    private void DistinctSteps()
    {
        if (Steps is null || Steps.Count == 0x0)
            return;

        Steps =
        [
            .. Steps
                .Select(selector: static step => new DecisionFlowStep
                {
                    NoSql = step.NoSql,
                    Components = step.Components,
                    Step = StepNameStripper(stepName: step.Step, takePos: 0x0, defValue: null),
                })
                .DistinctBy(keySelector: static step => step.Components),
        ];

        int refactorStepId = 0x0;
        Steps.ForEach(x =>
        {
            if (
                !x.Step.Equals(value: "start", comparisonType: StringComparison.OrdinalIgnoreCase)
                && !x.Step.Equals(value: "end", comparisonType: StringComparison.OrdinalIgnoreCase)
            )
                x.Step = $"step {++refactorStepId}";
        });
    }
}
