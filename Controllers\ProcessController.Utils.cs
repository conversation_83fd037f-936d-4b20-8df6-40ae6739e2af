using System.Text;
using idc.decision_cb.Utilities;
using Newtonsoft.Json.Linq;

namespace idc.decision_cb.Controllers;

public partial class ProcessController
{
    /// <summary>
    /// Class untuk mengkonversi nilai parameter menjadi string sesuai dengan tipe data yang diinginkan.
    /// </summary>
    internal class MasterParamFieldValueConverter
    {
        /// <summary>
        /// Nama parameter.
        /// </summary>
        internal required string Name { get; set; }

        /// <summary>
        /// Nilai parameter.
        /// </summary>
        internal required string Value { get; set; }

        /// <summary>
        /// Panjang nilai parameter. Opsional.
        /// </summary>
        public int? Length { get; internal set; }

        /// <summary>
        /// Tipe data parameter. Default adalah "varchar".
        /// </summary>
        public string DataType { get; internal set; } = "varchar";

        /// <summary>
        /// Mengkonversi nilai parameter menjadi string sesuai dengan tipe data yang diinginkan.
        /// </summary>
        /// <returns>Nilai parameter yang sudah dikonversi.</returns>
        /// <example>
        /// <code>
        /// var converter = new MasterParamFieldValueConverter
        /// {
        ///     Name = "MyParam",
        ///     Value = "v_10"
        /// };
        /// var result = converter.Extract();
        /// // result akan menjadi "varchar(10)"
        /// </code>
        /// </example>
        internal MasterParamFieldValueConverter Extract()
        {
            static string Translate(string value) =>
                value switch
                {
                    "b" => "BLOB",
                    "i" => "INTEGER",
                    "r" => "REAL",
                    "t" => "TEXT",
                    _ => "TEXT"
                };

            if (!Value.Contains(value: '_'))
            {
                DataType = Translate(value: Value);
            }
            else
            {
                string[] x = Value.Split(separator: '_');
                DataType = Translate(value: x[0]);
                Length = int.Parse(s: x[1]);
            }

            return this;
        }
    }

    /// <summary>
    /// Mengkonversi JObject menjadi list MasterParamFieldValueConverter
    /// dengan properti Name dan Value yang diisi berdasarkan key
    /// dan value dari JObject.
    /// </summary>
    /// <param name="json">JObject yang akan di konversi.</param>
    /// <returns>List MasterParamFieldValueConverter yang dihasilkan.</returns>
    /// <example>
    /// <code>
    /// var json = new JObject
    /// {
    ///     { "field1", "value1" },
    ///     { "field2", "value2" },
    ///     { "field3", "value3" }
    /// };
    /// var result = GetMasterFieldsValue(json);
    /// // result akan menjadi list of MasterParamFieldValueConverter
    /// // dengan properti Name dan Value yang diisi berdasarkan key
    /// // dan value dari JObject
    /// </code>
    /// </example>
    /// <remarks>
    /// Fungsi ini penting untuk memastikan bahwa nilai dari JObject
    /// dapat diolah dengan baik sesuai dengan tipe data yang diinginkan.
    /// </remarks>
    private static List<MasterParamFieldValueConverter>? ExtractMasterFieldsValue(JObject? json) =>
        json
            ?.Properties()
            .Select(static item =>
                new MasterParamFieldValueConverter
                {
                    Name = item.Name,
                    Value = item.Value?.ToString() ?? string.Empty,
                }.Extract()
            )
            .ToList();

    /// <summary>
    /// Mendapatkan JSON dari master parameter yang tersimpan di tabel <c>inmemory_de.master_mgmt</c>.
    /// Nilai yang diambil dari kolom <c>mm_json</c> dan <c>mdr_json->'field_name'</c>.
    /// Jika data tidak ditemukan, maka akan mengembalikan nilai null.
    /// </summary>
    /// <returns>JSON dari master parameter.</returns>
    /// <remarks>
    /// Fungsi ini penting untuk mendapatkan master parameter yang tersimpan di database.
    /// Nilai master parameter akan disimpan di cache agar mempercepat akses.
    /// </remarks>
    private string? GetMasterFieldsFromDB() =>
        Commons.GetConfigDE(iSqlite: iSqlite, docId: "MASTER_PARAM")?.ToString();

    /// <summary>
    /// Mendapatkan daftar parameter yang wajib diisi (master field) dari database.
    /// Nilai parameter akan dikonversi menjadi string sesuai dengan tipe data yang diinginkan.
    /// </summary>
    /// <returns>
    /// Daftar parameter yang wajib diisi. Nilai parameter dalam bentuk string.
    /// </returns>
    /// <example>
    /// <code>
    /// var masterFields = GetMasterFields();
    /// var result = masterFields?[0].Value; // e.g. "varchar(10)"
    /// </code>
    /// </example>
    /// <remarks>
    /// Metode ini digunakan untuk mendapatkan daftar parameter yang wajib diisi (master field)
    /// dari database. Nilai parameter akan dikonversi menjadi string sesuai dengan tipe data yang diinginkan.
    /// </remarks>
    private List<MasterParamFieldValueConverter>? GetMasterFields()
    {
        string? json = GetMasterFieldsFromDB();
        return json is null
            ? default
            : ExtractMasterFieldsValue(
                json: JObject.Parse(json).SelectToken("master_mgmt")?.ToObject<JObject>()
            );
    }

    /// <summary>
    /// Membuat query untuk membuat tabel sementara berdasarkan pada <paramref name="masterFields"/> yang diberikan.
    /// Nama tabel akan di format `TEMP_{<paramref name="appNo"/>}`.
    /// </summary>
    /// <param name="masterFields">Daftar field yang wajib diisi.</param>
    /// <param name="appNo">Nomor aplikasi.</param>
    /// <returns>Query string untuk membuat tabel sementara.</returns>
    /// <example>
    /// <code>
    /// var mandatoryFields = new List&lt;MasterParamFieldValueConverter&gt;
    /// {
    ///     new() { Name = "field1", DataType = "varchar" },
    ///     new() { Name = "field2", DataType = "int" },
    ///     new() { Name = "field3", DataType = "double" }
    /// };
    /// var appNo = "001";
    /// var result = GenerateTempTableQuery(mandatoryFields, appNo);
    /// // result akan menjadi "CREATE TEMP TABLE IF NOT EXISTS TEMP_001(field1 varchar, field2 int, field3 double);"
    /// </code>
    /// </example>
    /// <remarks>
    /// Metode ini penting untuk membuat query SQL untuk tabel sementara yang digunakan dalam aplikasi.
    /// </remarks>
    private static string GenerateTempTableQuery(
        List<MasterParamFieldValueConverter>? masterFields,
        string appNo
    )
    {
        if (masterFields is null)
            return string.Empty;

        StringBuilder queryBuilder = new($"CREATE TEMP TABLE IF NOT EXISTS TEMP_{appNo}(");

        foreach (MasterParamFieldValueConverter field in masterFields)
        {
            queryBuilder.Append(handler: $"{field.Name} {field.DataType}");

            if (field.Length.HasValue)
                queryBuilder.Append(handler: $"({field.Length.Value})");

            queryBuilder.Append(value: ", ");
        }

        // Hapus koma dan spasi terakhir
        queryBuilder.Length -= 2;

        return queryBuilder.Append(value: ");").ToString();
    }
}
