﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;

namespace idc.decision_cb.Utilities.Models.General.Input;

public class ExecuteComponentInputModel
{
    [JsonProperty("appno")]
    [Required(ErrorMessage = "ProcessStartInputModel: appno is required")]
    public required string AppNo { get; set; }

    [JsonProperty("code")]
    [Required(ErrorMessage = "ProcessStartInputModel: code is required")]
    public required string Code { get; set; }
}
