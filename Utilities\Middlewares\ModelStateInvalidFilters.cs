using idc.decision_cb.Utilities.Extensions;
using IDX.Utilities.Models.API;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Newtonsoft.Json.Linq;

namespace idc.decision_cb.Utilities.Middlewares;

public class ModelStateInvalidFilters(JToken language) : IActionFilter
{
    /// <summary>
    /// Called after the action method is invoked, but before the action result is executed.
    /// </summary>
    /// <param name="context">The context for the action execution.</param>
    public void OnActionExecuted(ActionExecutedContext context) { }

    /// <summary>
    /// Executes the action before it is executed and checks if the model state is valid.
    /// If the model state is not valid, it returns a BadRequestObjectResult with the appropriate error message.
    /// </summary>
    /// <param name="context">The action executing context.</param>
    public void OnActionExecuting(ActionExecutingContext context)
    {
        if (!context.ModelState.IsValid)
            context.Result = new BadRequestObjectResult(
                new ApiResponseData<List<string>?>()
                    .ChangeStatus(language, "fail")
                    .ChangeMessage(language, "request_validation_error")
                    .ChangeData(
                        [.. context
                            .ModelState.Values.SelectMany(e => e.Errors)
                            .Select(e => e.ErrorMessage.ParseLanguageToken(language))]
                    )
            );
    }
}
