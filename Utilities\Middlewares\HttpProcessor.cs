﻿using System.Net.Security;
using System.Text;
using IDX.Utilities;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Http;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace idc.decision_cb.Utilities.Middlewares;

public class GsmHttpProcessor(MemoryCacheService cache)
{
    private IConfigurationRoot? _config;

    public async Task<IConfigurationRoot> GetGSMConfigs(string path, string iKey)
    {
        List<dynamic>? cCache = cache.Get<List<dynamic>>(iKey) ?? [];
        if (cCache != default)
            return _config!;

        string urlApi =
            $"{Commons.ReadConfigurationValue<string>("Secret:urlAPI_idcconfig")}{path}";
        using HttpClient client = new ServiceCollection()
            .AddHttpClient()
            .Configure<HttpClientFactoryOptions>(
                name: "HttpClientWithSSLUntrusted",
                configureOptions: options =>
                {
                    options.HttpMessageHandlerBuilderActions.Add(item: builder =>
                        builder.PrimaryHandler = new HttpClientHandler
                        {
                            ServerCertificateCustomValidationCallback = (
                                message,
                                cert,
                                chain,
                                sslPolicyErrors
                            ) => sslPolicyErrors == SslPolicyErrors.None
                        }
                    );
                }
            )
            .BuildServiceProvider()
            .GetService<IHttpClientFactory>()!
            .CreateClient("HttpClientWithSSLUntrusted");
        client.BaseAddress = new Uri(urlApi);
        client.Timeout = TimeSpan.FromSeconds(
            Commons.ReadConfigurationValue<int>("Secret:timeOut")
        );

        JObject jobj = JObject.Parse(
            await (
                await client.PostAsync(
                    requestUri: urlApi,
                    content: new StringContent(
                        content: JsonConvert.SerializeObject(new { SecretId = iKey }),
                        encoding: Encoding.UTF8,
                        mediaType: "application/json"
                    )
                )
            ).Content.ReadAsStringAsync()
        );
        if (jobj["isSuccess"].CastToBoolean())
        {
            cache.Add(
                key: iKey,
                priority: CacheItemPriority.High,
                value: new List<dynamic>() { new { secret = jobj["secret"].CastToString() } }
            );
        }

        return _config = new ConfigurationBuilder()
            .SetBasePath(basePath: Directory.GetCurrentDirectory())
            .AddJsonStream(
                stream: new MemoryStream(
                    buffer: Encoding.UTF8.GetBytes(cache.Get<List<dynamic>>(iKey)![0].secret)
                )
            )
            .Build();
    }
}
