{"success": "<PERSON><PERSON><PERSON>", "fail": "Gaga<PERSON>", "data_request_success": "Proses pengambilan data ber<PERSON><PERSON> di<PERSON>ukan.", "save_data_success": "Data berhasil disimpan.", "check_data_success": "Proses checking data berhasil disimpan.", "revise_data_success": "Permintaan revisi berhasil disimpan.", "delete_data_success": "Proses menghapus data berhasil.", "approve_data_success": "Proses approval data berhasil disimpan.", "fetch_data_success": "Pengambilan data berhasil.", "fetch_data_fail": "Pengambilan data gagal, pastikan anda memberikan data dan argumen yang sesuai.", "update_data_success": "Proses editing data berhasil.", "request_validation_error": "<PERSON> kesalahan validasi permintaan.", "json_is_empty": "JSON kosong.", "json_props_incomplete": "Properti JSON tidak lengkap, field \"{field}\" tidak ditemukan.", "json_props_invalid": "Properti JSON tidak valid, field \"{field}\" tidak ditemukan atau nilainya kosong.", "query_builder_success": "Proses pembuatan kueri berhasil.", "not_found": "{0} tidak di<PERSON>.", "missing_fields": "Tidak ditemukan {0} field: {1}.", "missmatch_fields": "<PERSON> perbedaan {0} field: {1}."}