#!/bin/bash

CP=`cat  /apps/gitlab-runner/builds/mHDMZetj/0/idecision_source_net8/idc.decision_cb/Deployment  | grep -w "PROJECT" | head -1`

CV=`cat  /apps/gitlab-runner/builds/mHDMZetj/0/idecision_source_net8/idc.decision_cb/Deployment | grep -w "VERSION" | head -1`

CIMG=`cat  /apps/gitlab-runner/builds/mHDMZetj/0/idecision_source_net8/idc.decision_cb/Deployment | grep -w "DOCKERNIMAGES" | head -1 | sed 's/development/sit/g'`

CIMGLS=`cat  /apps/gitlab-runner/builds/mHDMZetj/0/idecision_source_net8/idc.decision_cb/Deployment | grep -w "DOCKERNIMAGESLS" | head -1 | sed 's/development/sit/g'`

CBUILD=`cat  /apps/gitlab-runner/builds/mHDMZetj/0/idecision_source_net8/idc.decision_cb/Deployment | grep -w "CBUILD" | head -1 | sed 's/development/sit/g'`

echo "$CP" >> /apps/gitlab-runner/builds/mHDMZetj/0/idecision_source_net8/idc.decision_cb/Deployment-sit

echo "$CV" >> /apps/gitlab-runner/builds/mHDMZetj/0/idecision_source_net8/idc.decision_cb/Deployment-sit

echo "$CIMG" >> /apps/gitlab-runner/builds/mHDMZetj/0/idecision_source_net8/idc.decision_cb/Deployment-sit

echo "$CIMGLS" >> /apps/gitlab-runner/builds/mHDMZetj/0/idecision_source_net8/idc.decision_cb/Deployment-sit

echo "$CBUILD" >> /apps/gitlab-runner/builds/mHDMZetj/0/idecision_source_net8/idc.decision_cb/Deployment-sit

check=`ls -Art /apps/gitlab-runner/builds/mHDMZetj/0/idecision_source_net8/idc.decision_cb/ | grep "Deployment-sit" | tail -n 1`

if [ "$check" = "Deployment-sit" ] ; then

		source /apps/gitlab-runner/builds/mHDMZetj/0/idecision_source_net8/idc.decision_cb/Deployment-sit
		
		export $(cut -d= -f1 /apps/gitlab-runner/builds/mHDMZetj/0/idecision_source_net8/idc.decision_cb/Deployment-sit)
		
		echo "`date` || File Deployment-sit is Ready $PROJECT $VERSION $DOCKERNIMAGES" >> /apps/script/activity_deploy.log
		
	else 
		
		echo "`date` || File not Found" >> /apps/script/activity_deploy.log 

		exit 1	

fi
