#!/bin/bash


check=`ls -Art /apps/gitlab-runner/builds/mHDMZetj/0/idecision_source_net8/idc.decision_cb/ | grep "Deployment" | tail -n 1`

if [ "$check" = "Deployment" ] ; then

                source /apps/gitlab-runner/builds/mHDMZetj/0/idecision_source_net8/idc.decision_cb/Deployment

                export $(cut -d= -f1 /apps/gitlab-runner/builds/mHDMZetj/0/idecision_source_net8/idc.decision_cb/Deployment)


                echo "`date` || File Deployment is Ready $PROJECT $VERSION $DOCKERNIMAGES" >> /apps/script/activity_deploy.log

        else

                echo "`date` || File not Found" >> /apps/script/activity_deploy.log

                exit 1

fi
