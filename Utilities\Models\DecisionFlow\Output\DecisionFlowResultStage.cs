using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;

namespace idc.decision_cb.Utilities.Models.DecisionFlow.Output;

public class DecisionFlowResultStage
{
    [JsonProperty("stage")]
    [Required(ErrorMessage = "DfResultStage: stage is required.")]
    public required string Stage { get; set; }

    [JsonProperty("components")]
    [Required(ErrorMessage = "DfResultStage: components is required.")]
    public required string Components { get; set; }

    [JsonProperty("result", NullValueHandling = NullValueHandling.Ignore)]
    public dynamic? Result { get; set; }

    [JsonProperty("timestamp", NullValueHandling = NullValueHandling.Ignore)]
    public string? Timestamp { get; set; }
}
