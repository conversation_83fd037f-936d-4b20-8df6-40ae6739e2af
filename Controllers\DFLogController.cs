using idc.decision_cb.Utilities;
using IDX.Utilities;
using IDX.Utilities.DataProcessor;
using IDX.Utilities.Models.API;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;
using NpgsqlTypes;

namespace idc.decision_cb.Controllers;

[Route("api/[controller]")]
[ApiController]
public class DFLogController(SystemLogging logging, JToken language, PgSqlHelper pgHelper)
    : ControllerBase
{
    [HttpPost("DFProcResult")]
    public async Task<ApiResponseData<object>> DFProcResult(
        [FromBody] JObject data,
        CancellationToken cancellationToken = default
    )
    {
        try
        {
            await pgHelper.ConnectAsync(cancellationToken: cancellationToken);
            var resp = await pgHelper.ExecuteScalarAsync<string?>(
                cancellationToken: cancellationToken,
                callback: async _ => await Task.CompletedTask,
                spCallInfo: new()
                {
                    Schema = "inmemory_de",
                    SPName = "result_de_upsert",
                    Parameters =
                    [
                        new()
                        {
                            Name = "p_name_id",
                            Value = data["app_no"]?.ToObject<string>(),
                            DataType = NpgsqlDbType.Text
                        },
                        new()
                        {
                            Name = "p_value",
                            Value = data["data"]?.ToObject<string>(),
                            DataType = NpgsqlDbType.Text
                        },
                        new()
                        {
                            Name = "p_result",
                            Value = data["apiResponse"]?.ToObject<string>(),
                            DataType = NpgsqlDbType.Text
                        },
                        new()
                        {
                            Name = "p_chars",
                            Value = data["scc_chars"]?.ToObject<string>(),
                            DataType = NpgsqlDbType.Text
                        },
                    ]
                }
            );
            return new ApiResponseData<object>().ChangeData(resp);
        }
        catch (Exception ex)
        {
            return new ApiResponseData<object>()
                .ChangeStatus(languageToken: language, propertyName: "fail")
                .ChangeMessage(
                    exception: ex,
                    logging: logging,
                    includeStackTrace: Commons.IsDevelopmentMode()
                );
        }
    }
}
