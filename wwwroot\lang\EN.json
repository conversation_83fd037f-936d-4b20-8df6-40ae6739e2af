{"success": "Success", "fail": "Failure", "data_request_success": "The data collection process was successfully carried out.", "save_data_success": "The data is successfully stored.", "check_data_success": "The data checking process was successfully stored.", "revise_data_success": "Revision requests were successfully stored.", "delete_data_success": "The process of deleting data is successful.", "approve_data_success": "The process of approving data is successful.", "fetch_data_success": "Fetching data successful.", "fetch_data_fail": "Fetching data fail, make sure you provide appropriate data and arguments.", "update_data_success": "The data editing process was successful.", "request_validation_error": "There was a request validation error.", "json_is_empty": "The JSON is empty.", "json_props_incomplete": "The JSON properties are incomplete, field \"{field}\" is missing.", "json_props_invalid": "The JSON properties are invalid, field \"{field}\" is missing or the value is empty.", "query_builder_success": "The query builder process was successful.", "not_found": "{0} not found.", "missing_fields": "Missing {0} fields: {1}.", "missmatch_fields": "Missmatch {0} fields: {1}."}