using System.Data;
using System.Runtime.InteropServices.JavaScript;
using System.Xml.Linq;
using Google.Protobuf.WellKnownTypes;
using idc.decision_cb.Utilities;
using idc.decision_cb.Utilities.Constants;
using idc.decision_cb.Utilities.Extensions;
using idc.decision_cb.Utilities.Middlewares;
using idc.decision_cb.Utilities.Models.DecisionFlow.Input;
using idc.decision_cb.Utilities.Models.DecisionFlow.Output;
using idc.decision_cb.Utilities.Models.Enums;
using idc.decision_cb.Utilities.Models.General.Input;
using IDX.Utilities;
using IDX.Utilities.DataProcessor;
using IDX.Utilities.Models.API;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace idc.decision_cb.Controllers;

[Route("api/[controller]")]
[ApiController]
public partial class ProcessController(
    SystemLogging logging,
    JToken language,
    MultipleSqliteInstance iSqlite,
    MemoryCacheService cache,
    IConfigurationRoot configuration,
    HttpClient httpClient,
    PgSqlHelper pgHelper
) : ControllerBase
{
    private readonly string resultStr = "result";
    private readonly string resultsStr = "results";

    private int statusCode = 200;
    private string dateStart = "";

    /// <summary>
    /// Get a json configuration from component.
    /// </summary>
    /// <param name="inputJson">The key value to get configuration for.</param>
    /// <param name="cancellationToken">The cancellation token for the operation.</param>
    /// <returns>The generated query.</returns>
    /// <example>
    /// <code>
    /// {
    ///     "key_name": "dtr_code",
    ///     "key_value": "DTR00195",
    /// }
    /// </code>
    /// </example>
    [HttpPost("GetConfiguration")]
    public async Task<ApiResponseData<JObject>> GetConfigurationComponent(
        [FromBody] ConfigComponentInputModel inputJson,
        CancellationToken cancellationToken = default
    )
    {
        ApiResponseData<JObject> result = new();

        try
        {
            await Task.Run(
                () =>
                {
                    result
                        .ChangeStatus(
                            languageToken: language,
                            propertyName: GeneralConstant.API_RESP_SUCCESS
                        )
                        .ChangeMessage(
                            languageToken: language,
                            propertyName: GeneralConstant.API_RESP_REQ_SUCCESS
                        )
                        .ChangeData(
                            data: GetComponentConfigPG(
                                    componentCode: inputJson.KeyValue,
                                    component: DecisionComponent.GetByKeyName(inputJson.KeyName)
                                )
                                .ToJObject()
                        );
                },
                cancellationToken: cancellationToken
            );
        }
        catch (Exception ex)
        {
            result
                .ChangeStatus(
                    languageToken: language,
                    propertyName: GeneralConstant.API_RESP_FAILED
                )
                .ChangeMessage(
                    exception: ex,
                    logging: logging,
                    includeStackTrace: Commons.IsDevelopmentMode()
                );
        }

        return result;
    }

    /// <summary>
    /// Get a decision result based on component.
    /// </summary>
    /// <param name="inputJson">The key value to get configuration for.</param>
    /// <param name="cancellationToken">The cancellation token for the operation.</param>
    /// <returns>The generated query.</returns>
    /// <example>
    /// <code>
    /// {
    ///     "code": "D10157" // code component
    ///     "appno": "123" // cf_los_app_no
    /// }
    /// </code>
    /// </example>
    [HttpPost("ExecuteComponent")]
    public async Task<IActionResult> ExecuteComponent(
        [FromBody] ExecuteComponentInputModel inputJson,
        CancellationToken cancellationToken = default
    )
    {
        ApiResponseData<DecisionFlowResult> result = new();

        try
        {
            result
                .ChangeStatus(language, GeneralConstant.API_RESP_SUCCESS)
                .ChangeMessage(language, GeneralConstant.API_RESP_REQ_SUCCESS)
                .ChangeData(await GetResultComponent(inputJson));
        }
        catch (Exception ex)
        {
            statusCode = 500;
            result
                .ChangeStatus(
                    languageToken: language,
                    propertyName: GeneralConstant.API_RESP_FAILED
                )
                .ChangeMessage(
                    exception: ex,
                    logging: logging,
                    includeStackTrace: Commons.IsDevelopmentMode()
                );
        }

        return StatusCode(statusCode, result);
    }

    /// <summary>
    /// Validate the mandatory and missing field type in the Decision Form for the given Application Number.
    /// </summary>
    /// <param name="dataJson">The JSON data containing the application number.</param>
    /// <returns>The result of the validation.</returns>
    /// <remarks>
    /// <para>
    /// Json sample =
    /// {
    ///     "cf_los_app_no": "123"
    /// }
    /// </para>
    /// </remarks>
    [HttpPost("Prerequisites")]
    public IActionResult Prerequisites([FromBody] JObject dataJson)
    {
        ApiResponseData<JObject> result = new();

        try
        {
            ExecPrerequisite(dataJson);
            result
                .ChangeStatus(
                    languageToken: language,
                    propertyName: GeneralConstant.API_RESP_SUCCESS
                )
                .ChangeMessage(
                    languageToken: language,
                    propertyName: GeneralConstant.API_RESP_REQ_SUCCESS
                );
        }
        catch (Exception ex)
        {
            statusCode = 500;
            result
                .ChangeStatus(
                    languageToken: language,
                    propertyName: GeneralConstant.API_RESP_FAILED
                )
                .ChangeMessage(
                    exception: ex,
                    logging: logging,
                    includeStackTrace: Commons.IsDevelopmentMode()
                );
        }

        return StatusCode(statusCode, result);
    }

    // TODO: Move Log Write into a Service
    // TODO: Move Prequisite into a Memory

    /// <summary>
    /// Start the Decision process for the given Application Number.
    /// </summary>
    /// <param name="inputJson">The JSON data containing the application number and the Decision component code.</param>
    /// <param name="cancellationToken">The cancellation token for the operation.</param>
    /// <returns>The result of the process.</returns>
    /// <remarks>
    /// This example demonstrates how to start the decision process using a JSON object with the required code and application number.
    /// <para>
    /// Json sample =
    /// {
    ///     "code": "D12345",
    ///     "data": [
    ///         {
    ///             "cf_los_app_no": "123",
    ///             ....
    ///             Mandatory fields dari inmemory.master_data_reff
    ///             ....
    ///         }
    ///     ]
    /// }
    /// </para>
    /// </remarks>
    [HttpPost("Start")]
    public async Task<IActionResult> Start(
        [FromBody] JObject inputJson,
        CancellationToken cancellationToken = default
    )
    {
        ApiResponseData<DecisionFlowResult> result = new();

        try
        {
            dateStart = DateTime.Now.ToString(GeneralConstant.DEFVAL_DATETIME_FMT_YMDHMSF);

            string code = inputJson["code"].CastToString();
            JObject data =
                inputJson["data"]?[0].CastToString().ToJObject()
                ?? throw new DataException("Invalid data");

            ExecPrerequisite(data);

            bool isSimulation = inputJson["isSimulation"]?.CastToBoolean() ?? false;

            DecisionFlowResult joResult = await GetResultComponent(
                new() { Code = code, AppNo = data["cf_los_app_no"].CastToString() },
                isSimulation,
                inputJson.ContainsKey("DF")
                    ? inputJson["DF"]?.ToObject<DecisionFlowConfigurationInputModel>()
                    : null
            );

            joResult.Parameters = GetParamResult(code, data["cf_los_app_no"].CastToString()) ?? [];
            joResult.TempResults = GetTempResults(data["cf_los_app_no"].CastToString()) ?? [];

            result
                .ChangeStatus(
                    languageToken: language,
                    propertyName: GeneralConstant.API_RESP_SUCCESS
                )
                .ChangeMessage(
                    languageToken: language,
                    propertyName: GeneralConstant.API_RESP_REQ_SUCCESS
                )
                .ChangeData(data: joResult);
        }
        catch (Exception ex)
        {
            statusCode = 500;
            result
                .ChangeStatus(
                    languageToken: language,
                    propertyName: GeneralConstant.API_RESP_FAILED
                )
                .ChangeMessage(
                    exception: ex,
                    logging: logging,
                    includeStackTrace: Commons.IsDevelopmentMode()
                );
        }
        finally
        {
            iSqlite
                .InMemory.Connect()
                .ExecuteQuery(
                    query: $"DROP TABLE IF EXISTS TEMP_{inputJson?["data"]?[0]?["cf_los_app_no"].CastToString()};",
                    data: out JObject? _
                );
        }

        return StatusCode(statusCode, result);
    }

    /// <summary>
    /// Validates the provided SQL query using a temporary table and returns the result.
    /// </summary>
    /// <param name="inputJson">The JSON object containing the query to be validated.</param>
    /// <returns>An ApiResponseData&lt;JObject&gt; indicating the success or failure of the query validation.</returns>
    /// <example>
    /// <code>
    /// {
    ///     "query": "SELECT column_name"
    /// }
    /// </code>
    /// </example>
    /// <remarks>
    /// The method creates a temporary table using the current timestamp as the table name,
    /// executes the provided query against this temporary table, and then drops the table.
    /// <para> Json sample = { "query" : "SELECT column_name" } </para>
    /// </remarks>
    [HttpPost("ValidateQuery")]
    public ApiResponseData<JObject> ValidateQuery([FromBody] JObject inputJson)
    {
        ApiResponseData<JObject> result = new();
        string appNo = DateTime.Now.ToString("yyyyMMddHHmmssfff");

        try
        {
            string inputQuery = inputJson["query"].CastToString().Replace("'", "'''");

            iSqlite
                .InMemory.Connect()
                .ExecuteQuery(
                    query: $@"
                        {GenerateTempTableQuery(masterFields: GetMasterFields(), appNo: appNo)}; 
                        {inputQuery} FROM TEMP_{appNo};
                    ",
                    data: out JObject? _
                );

            result
                .ChangeStatus(
                    languageToken: language,
                    propertyName: GeneralConstant.API_RESP_SUCCESS
                )
                .ChangeMessage(
                    languageToken: language,
                    propertyName: GeneralConstant.API_RESP_QUE_BUILDER_SUCCESS
                );
        }
        catch (Exception ex)
        {
            result
                .ChangeStatus(
                    languageToken: language,
                    propertyName: GeneralConstant.API_RESP_FAILED
                )
                .ChangeMessage(
                    exception: ex,
                    logging: logging,
                    includeStackTrace: Commons.IsDevelopmentMode()
                );
        }
        finally
        {
            iSqlite
                .InMemory.Connect()
                .ExecuteQuery($"DROP TABLE IF EXISTS TEMP_{appNo};", out JObject? _);
        }

        return result;
    }

    [HttpPost("Execute")]
    public async Task<IActionResult> Execute(
     [FromBody] JObject inputJson,
     CancellationToken cancellationToken = default
 )
    {
        var joData = new JObject();
        var jaDataRes = new JArray();
        int statusCode = 200;
        string strStatus = "OK";
        try
        {
            var resultData = await Start(inputJson, cancellationToken);

            if (resultData is ObjectResult objectResult)
            {
                statusCode = objectResult.StatusCode ?? 200;

                var resp = JObject.FromObject(objectResult.Value);
                var dataRes = new JObject();

                var hasData = resp.TryGetValue("Data", out var dataToken);
                var hasStatus = resp.TryGetValue("Status", out var statusToken);
                var hasMessage = resp.TryGetValue("Message", out var messageToken);

                if (hasData && dataToken?.Type == JTokenType.Object)
                {
                    var dataObj = (JObject)dataToken;
                    if (dataObj.TryGetValue("parameter_results", out var paramResultToken)
                        && paramResultToken.Type == JTokenType.Object)
                    {
                        dataRes = (JObject)paramResultToken;
                    }
                }
                else if (hasMessage)
                {
                    string stsMessage = messageToken?.ToString() ?? "";
                    dataRes["error"] = stsMessage;
                }

                if (hasStatus)
                {
                    strStatus = statusToken?.ToString() ?? "OK";
                    if (strStatus == "Success")
                        strStatus = "OK";
                }

                jaDataRes.Add(dataRes);
            }
        }
        catch (Exception ex)
        {
            statusCode = 500;
            strStatus = "Internal Server Error";

            var errorResponse = new ApiResponseData<DecisionFlowResult>();
            errorResponse
                .ChangeStatus(languageToken: language, propertyName: GeneralConstant.API_RESP_FAILED)
                .ChangeMessage(exception: ex, logging: logging, includeStackTrace: Commons.IsDevelopmentMode());

            var errorResult = new JObject
            {
                ["code"] = statusCode,
                ["Status"] = strStatus,
                ["data"] = new JArray { new JObject { ["error"] = ex.Message } }
            };

            return StatusCode(statusCode, errorResult);
        }

        joData["code"] = statusCode;
        joData["Status"] = strStatus;
        joData["data"] = jaDataRes;

        return StatusCode(statusCode, joData);
    }
}
