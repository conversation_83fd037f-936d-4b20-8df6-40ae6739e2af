using System.Data;
using Newtonsoft.Json.Linq;

namespace idc.decision_cb.Utilities.Extensions;

public static class JObjectExtensions
{
    /// <summary>
    /// Validates that the properties in the JSON object exist and have values if specified.
    /// Throws a DataException if any property is missing or has an empty value.
    /// </summary>
    /// <param name="obj">The JSON object to validate.</param>
    /// <param name="props">The properties to validate.</param>
    /// <param name="langToken">The language token for parsing output.</param>
    /// <param name="validateValue">(Optional) Whether to include the value check. Default is true.</param>
    /// <exception cref="DataException">Thrown when a property is missing or has an empty value.</exception>
    internal static void ValidateProps(
        this JObject? obj,
        string[] props,
        JToken langToken,
        bool validateValue = true
    )
    {
        if (obj is null)
            throw new DataException("json_is_empty".ParseLanguageToken(langToken: langToken));

        List<string> requiredProps = [.. props.Where(prop => !obj.TryGetValue(prop, out _))];

        if (requiredProps.Any())
            throw new DataException(
                validateValue
                    ? "json_props_invalid".ParseLanguageToken(
                        langToken: langToken,
                        replacement: new Dictionary<string, object>()
                        {
                            { "{field}", requiredProps.First() }
                        }
                    )
                    : "json_props_incomplete".ParseLanguageToken(
                        langToken: langToken,
                        replacement: new Dictionary<string, object>()
                        {
                            { "{field}", requiredProps.First() }
                        }
                    )
            );

        if (validateValue)
        {
            List<string> invalidProps = [.. props.Where(prop => obj![prop] is null || obj![prop]?.ToString() == "")];

            if (invalidProps.Any())
                throw new DataException(
                    "json_props_invalid".ParseLanguageToken(
                        langToken: langToken,
                        replacement: new Dictionary<string, object>()
                        {
                            { "{field}", invalidProps.First() }
                        }
                    )
                );
        }
    }
}
