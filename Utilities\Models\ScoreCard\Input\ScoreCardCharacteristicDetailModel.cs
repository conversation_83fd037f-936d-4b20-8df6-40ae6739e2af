using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;

namespace idc.decision_cb.Utilities.Models.ScoreCard.Input;

/// <summary>
/// Represents a detail of a score card characteristic.
/// </summary>
/// <example>
/// {
///     "limit": "18",
///     "score": "0",
///     "baseline": "true"
/// }
/// </example>
public class ScoreCardCharacteristicDetailModel
{
    /// <summary>
    /// The base line for a score card characteristic detail.
    /// </summary>
    /// <example>"true"</example>
    [JsonProperty("baseline")]
    [Required(ErrorMessage = "ScoreCardCharacteristicDetailModel: BaseLine is required.")]
    public required string BaseLine { get; set; }

    /// <summary>
    /// The limit for a score card characteristic detail.
    /// </summary>
    /// <example>"18"</example>
    [JsonProperty("limit")]
    [Required(ErrorMessage = "ScoreCardCharacteristicDetailModel: Limit is required.")]
    public required string Limit { get; set; }

    /// <summary>
    /// The score for a score card characteristic detail.
    /// </summary>
    /// <example>"0"</example>
    [JsonProperty("score")]
    [Required(ErrorMessage = "ScoreCardCharacteristicDetailModel: Score is required.")]
    public required string Score { get; set; }
}
