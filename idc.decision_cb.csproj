<Project Sdk="Microsoft.NET.Sdk.Web">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <InvariantGlobalization>true</InvariantGlobalization>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <NoWarn>$(NoWarn);1591;1711</NoWarn>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="8.0.1" />
    <PackageReference Include="Microsoft.Data.Sqlite" Version="9.0.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="9.0.2" />
    <PackageReference Include="OpenTelemetry" Version="1.11.1" />
    <PackageReference Include="OpenTelemetry.AutoInstrumentation" Version="1.10.0" />
    <PackageReference Include="OpenTelemetry.Exporter.Console" Version="1.11.1" />
    <PackageReference Include="OpenTelemetry.Exporter.OpenTelemetryProtocol" Version="1.11.1" />
    <PackageReference Include="OpenTelemetry.Extensions.Hosting" Version="1.11.1" />
    <PackageReference Include="OpenTelemetry.Instrumentation.AspNetCore" Version="1.11.0" />
    <PackageReference Include="OpenTelemetry.Instrumentation.Http" Version="1.11.0" />
    <PackageReference Include="OpenTelemetry.Instrumentation.Runtime" Version="1.11.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.5.0" />
    <PackageReference Include="System.Net.Http" Version="4.3.4" />
    <PackageReference Include="CouchbaseNetClient" Version="3.6.3" />
    <PackageReference Include="SqlKata" Version="2.4.0" />
  </ItemGroup>
  <ItemGroup>
    <None Include="wwwroot\**">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>
  <ItemGroup>
    <Reference Include="IDX.Utilities">
      <HintPath>wwwroot\dependencies\IDX.Utilities.dll</HintPath>
    </Reference>
  </ItemGroup>
</Project>