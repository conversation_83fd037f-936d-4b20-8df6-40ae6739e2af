using System.Data.Common;
using idc.decision_cb.Utilities;
using idc.decision_cb.Utilities.Middlewares;
using IDX.Utilities;
using IDX.Utilities.DataProcessor;
using IDX.Utilities.Models;
using Microsoft.Data.Sqlite;
using Newtonsoft.Json.Linq;
using static IDX.Utilities.DataProcessor.PgSqlHelper;

namespace idc.decision_cb;

public static partial class Program
{
    /// <summary>
    /// Sets up Couchbase database if enabled in the configuration.
    /// </summary>
    /// <param name="services">The service collection to add the services to.</param>
    /// <param name="appSettings">The application settings.</param>
    /// <returns>The modified service collection.</returns>
    private static IServiceCollection DISetupCouchbase(
        this IServiceCollection services,
        IConfigurationRoot appSettings
    )
    {
        if (appSettings.ReadConfigurationValue<bool>($"{ParentSettingName}:DI:couchbase"))
        {
            string? defConnString = appSettings.ReadConfigurationValue<string>(
                $"{ParentSettingName}:DI:couchbase_default_constring"
            );

            if (string.IsNullOrWhiteSpace(defConnString))
                return services;

            services.AddScoped(provider => new CouchBaseHelper(
                connectionString: new CouchBaseConnectionString
                {
                    Host =
                        $"couchbase://{appSettings.ReadConfigurationValue<string>($"CBContextSettings:{defConnString}:host")}",
                    Port = appSettings.ReadConfigurationValue<int>(
                        $"CBContextSettings:{defConnString}:Port"
                    ),
                    Username = appSettings.ReadConfigurationValue<string>(
                        $"CBContextSettings:{defConnString}:Username"
                    ),
                    Password = appSettings.ReadConfigurationValue<string>(
                        $"CBContextSettings:{defConnString}:Password"
                    )
                },
                encryptionKey: appSettings.ReadConfigurationValue<string>(
                    "KeyConvert:EncryptionKey"
                ),
                encryptionSalts: appSettings.ReadConfigurationValue<byte[]>(
                    "KeyConvert:EncryptionSalts"
                )
            ));
        }

        return services;
    }

    /// <summary>
    /// Sets up HTTP client if enabled in the configuration.
    /// </summary>
    /// <param name="services">The service collection to add the services to.</param>
    /// <param name="appSettings">The application settings.</param>
    /// <returns>The modified service collection.</returns>
    private static IServiceCollection DISetupHttpClient(
        this IServiceCollection services,
        IConfigurationRoot appSettings
    )
    {
        if (appSettings.ReadConfigurationValue<bool>(name: $"{ParentSettingName}:DI:httpClient"))
        {
            services.AddSingleton(static provider =>
                provider
                    .GetRequiredService<IHttpClientFactory>()
                    .CreateClient(name: "HttpClientWithSSLUntrusted")
            );
        }

        return services;
    }

    /// <summary>
    /// Sets up language if enabled in the configuration.
    /// </summary>
    /// <param name="services">The service collection to add the services to.</param>
    /// <param name="appSettings">The application settings.</param>
    /// <returns>The modified service collection.</returns>
    private static IServiceCollection DISetupLanguage(
        this IServiceCollection services,
        IConfigurationRoot appSettings
    )
    {
        services.AddSingleton(provider =>
        {
            string defLang =
                appSettings
                    .ReadConfigurationValue<string>($"{ParentSettingName}:Language")
                    ?.ToUpper() ?? "EN";

            string json = File.ReadAllText(
                path: Path.Combine(
                    path1: AppContext.BaseDirectory,
                    path2: $"wwwroot/lang/{defLang}.json"
                )
            );

            return JToken.Parse(json);
        });

        return services;
    }

    /// <summary>
    /// Sets up memory cache if enabled in the configuration.
    /// </summary>
    /// <param name="services">The service collection to add the services to.</param>
    /// <param name="appSettings">The application settings.</param>
    /// <returns>The modified service collection.</returns>
    private static IServiceCollection DISetupMemCache(
        this IServiceCollection services,
        IConfigurationRoot appSettings
    )
    {
        if (appSettings.ReadConfigurationValue<bool>(name: $"{ParentSettingName}:DI:memCache"))
        {
            services
                .AddMemoryCache()
                .AddSingleton(provider => new MemoryCacheService(
                    absoluteExpirationValue: appSettings.ReadConfigurationValue<int>(
                        name: "Secret:InMemoryCacheConfigurations:AbsoluteExpiration"
                    ),
                    slidingExpirationValue: appSettings.ReadConfigurationValue<int>(
                        name: "Secret:InMemoryCacheConfigurations:SlidingExpiration"
                    )
                ));
        }

        return services;
    }

    /// <summary>
    /// Sets up PostgreSQL database if enabled in the configuration.
    /// </summary>
    /// <param name="services">The service collection to add the services to.</param>
    /// <param name="appSettings">The application settings.</param>
    /// <returns>The modified service collection.</returns>
    private static IServiceCollection DISetupPgSql(
        this IServiceCollection services,
        IConfigurationRoot appSettings
    )
    {
        if (appSettings.ReadConfigurationValue<bool>($"{ParentSettingName}:DI:pgSql"))
        {
            string? defConnString = appSettings.ReadConfigurationValue<string>(
                $"{ParentSettingName}:DI:pgsql_default_constring"
            );

            if (string.IsNullOrWhiteSpace(defConnString))
                return services;

            services.AddScoped(provider => new PgSqlHelper(
                options: new ConStringFromJson
                {
                    JsonFilePath = "appsettings.json",
                    ConfigName = $"DbContextSettings:{defConnString}",
                    PassName = "configPass:passwordDB",
                    EncryptionKey = "KeyConvert:EncryptionKey",
                    EncryptionSalts = "KeyConvert:EncryptionSalts"
                }
            ));
        }

        return services;
    }

    /// <summary>
    /// Sets up SqLite database if enabled in the configuration.
    /// </summary>
    /// <param name="services">The service collection to add the services to.</param>
    /// <param name="appSettings">The application settings.</param>
    /// <returns>The modified service collection.</returns>
    /// <remarks>
    /// This method sets up the SqLite database if the configuration value
    /// <c>idc.template.DI:sqlite</c> is set to true. The SqLite database connection string
    /// is determined by the configuration value
    /// <c>idc.template.DI:sqlite_default_constring</c>. If the value is not set, the method
    /// will not add any SqLite database connection.
    /// </remarks>
    private static IServiceCollection DISetupSQLite(
        this IServiceCollection services,
        IConfigurationRoot appSettings
    )
    {
        if (appSettings.ReadConfigurationValue<bool>($"{ParentSettingName}:DI:sqlite"))
        {
            string? defConnString = appSettings.ReadConfigurationValue<string>(
                $"{ParentSettingName}:DI:sqlite_default_constring"
            );

            if (string.IsNullOrWhiteSpace(defConnString))
                return services;

            DbConnectionStringBuilder builderSqliteFile =
                new()
                {
                    ConnectionString = appSettings.ReadConfigurationValue<string>(
                        $"SqLiteContextSettings:{defConnString}"
                    )
                };

            builderSqliteFile.TryGetValue("Data Source", out object? fileSource);

            services.AddScoped(provider => new MultipleSqliteInstance(
                inMemory: new SqLiteHelper(
                    appSettings.ReadConfigurationValue<string>($"SqLiteContextSettings:memory")
                ),
                file: new SqLiteHelper(
                    new SqliteConnectionStringBuilder
                    {
                        DataSource = fileSource.CastToString(),
                        Cache = SqliteCacheMode.Shared,
                        Mode = SqliteOpenMode.ReadOnly
                    }
                )
            ));
        }

        return services;
    }
}
