namespace idc.decision_cb.Utilities.Constants;

internal static class GeneralConstant
{
    // API CONFIGURATIONS
    internal const string API_CONTENT_TYPE = "application/json";

    // API RESPONSE
    internal const string API_RESP_FAILED = "fail";
    internal const string API_RESP_NOT_FOUND = "not_found";
    internal const string API_RESP_QUE_BUILDER_SUCCESS = "query_builder_success";
    internal const string API_RESP_REQ_SUCCESS = "data_request_success";
    internal const string API_RESP_SUCCESS = "success";

    // DEFAULT VALUE
    internal const string DEFVAL_STR_ELLIPSIS = "...";
    internal const string DEFVAL_DATETIME_FMT_YMD = "yyyy-MM-dd";
    internal const string DEFVAL_DATETIME_FMT_YMDHMS = "yyyy-MM-dd HH:mm:ss";
    internal const string DEFVAL_DATETIME_FMT_YMDHMSF = "yyyy-MM-dd HH:mm:ss.fff";

    // DEBUGING MODE
    internal const string DBG_DEBUG = "DEBUG";
    internal const string DBG_DEVELOPMENT = "Development";
    internal const string DBG_ENVIRONMENT = "ASPNETCORE_ENVIRONMENT";

    // SQLITE
    internal const string DB_CONFIGS_TABLES = "Decision_NewGen_Configurations";
    internal const string DB_PARAMRESULTS_TABLES = "Decision_NewGen_ParameterResults";

    internal static readonly string DB_DF_QUERY_GENERATOR =
        @"
        UPDATE
            TEMP_{{IdTempTable}}
        SET
            {{field_set}}
        WHERE
            cf_los_app_no = '{{IdTempTable}}'
        RETURNING 
            {{return_value}}
        ;
    ";

    internal static string DB_DF_CONFIG_SELECT_QUERY(string id) =>
        $@"
            SELECT 
                cd.DOCUMENTS 
            FROM 
                {DB_CONFIGS_TABLES} AS cd 
            WHERE 
                cd.ID = '{id}'
        ";

    internal static string DB_DF_PARAMRESULTS_SELECT_QUERY(string id) =>
        $@"
            SELECT 
                p.DOCUMENTS 
            FROM 
                {DB_PARAMRESULTS_TABLES} AS p 
            WHERE 
                p.ID = '{id}'
        ";
}
