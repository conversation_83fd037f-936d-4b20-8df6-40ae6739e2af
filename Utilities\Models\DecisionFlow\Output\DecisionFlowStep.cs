using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace idc.decision_cb.Utilities.Models.DecisionFlow.Output;

/// <summary>
/// Represents a decision flow step.
/// </summary>
public class DecisionFlowStep
{
    /// <summary>
    /// Gets or sets the components.
    /// </summary>
    /// <value>The components.</value>
    [JsonProperty("components"), Required(ErrorMessage = "DfStep: components is required.")]
    public required string Components { get; set; }

    /// <summary>
    /// Gets or sets the no sql.
    /// </summary>
    /// <value>The no sql.</value>
    [JsonProperty(propertyName: "no_sql", NullValueHandling = NullValueHandling.Ignore)]
    public string? NoSql { get; set; }

    /// <summary>
    /// Gets or sets the step name.
    /// </summary>
    /// <value>The step name.</value>
    [JsonProperty("step"), Required(ErrorMessage = "DfStep: step is required.")]
    public required string Step { get; set; }

    /// <summary>
    /// Gets or sets the scorecard characteristics.
    /// </summary>
    [JsonProperty("scc_chars")]
    public JObject ScorecardCharacteristics { get; set; } = [];
}
