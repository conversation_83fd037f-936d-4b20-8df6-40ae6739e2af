using IDX.Utilities.DataProcessor;
using NpgsqlTypes;

namespace idc.decision_cb.Utilities.Helpers;

internal static class PgCommonDataHelper
{
    /// <summary>
    /// Menyimpan konfigurasi component ke dalam database PostgreSQL.
    /// </summary>
    /// <typeparam name="T">Tipe data yang akan dihasilkan.</typeparam>
    /// <param name="pgHelper">Objek PgSqlHelper yang digunakan untuk menghubungi database.</param>
    /// <param name="keyName">Nama kunci dari konfigurasi component yang akan disimpan.</param>
    /// <param name="keyValue"><PERSON><PERSON> kunci dari konfigurasi component yang akan disimpan.</param>
    /// <param name="configurationJson">Konfigurasi component dalam format JSON.</param>
    /// <returns><PERSON><PERSON> yang dihasilkan dari penyimpanan konfigurasi component.</returns>
    /// <example>
    /// <code>
    /// PgSqlHelper pgHelper = new PgSqlHelper("Server=127.0.0.1;Port=5432;Database=mydb;User Id=myuser;Password=mypassword;");
    /// string keyName = "mycomponent";
    /// string keyValue = "myvalue";
    /// string configurationJson = "{\"mysetting\":\"myvalue\"}";
    ///
    /// var result = pgHelper.WriteComponentsConfiguration&lt;bool&gt;(keyName, keyValue, configurationJson);
    /// </code>
    /// </example>
    internal static T? WriteComponentsConfiguration<T>(
        this PgSqlHelper pgHelper,
        string keyName,
        string keyValue,
        string configurationJson
    )
    {
        pgHelper
            .Connect()
            .ExecuteScalar<T>(
                spCallInfo: new()
                {
                    Schema = "workflow",
                    SPName = "cb_component_de_cb_upsert",
                    Parameters =
                    [
                        new()
                        {
                            Name = "p_key_name",
                            Value = keyName,
                            DataType = NpgsqlDbType.Text
                        },
                        new()
                        {
                            Name = "p_key_value",
                            Value = keyValue,
                            DataType = NpgsqlDbType.Text
                        },
                        new()
                        {
                            Name = "p_value",
                            Value = configurationJson,
                            DataType = NpgsqlDbType.Text
                        }
                    ]
                },
                out T? result
            );
        return result;
    }
}
