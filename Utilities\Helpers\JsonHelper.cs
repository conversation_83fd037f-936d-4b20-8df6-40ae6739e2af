using Newtonsoft.Json.Linq;

namespace idc.decision_cb.Utilities.Helpers;

internal static class <PERSON>sonHelper
{
    /// <summary>
    /// Removes any empty children from a JSON token. If the token is an object, empty properties are removed.
    /// If the token is an array, empty items are removed. If the token is neither an object nor an array,
    /// the token is returned unchanged.
    /// </summary>
    /// <param name="token">The JSON token to remove empty children from.</param>
    /// <returns>The JSON token with empty children removed.</returns>
    internal static JToken RemoveEmptyChildren(JToken token) =>
        token.Type switch
        {
            JTokenType.Object => RemoveEmptyFromObject((JObject)token),
            JTokenType.Array => RemoveEmptyFromArray((JArray)token),
            _ => token,
        };

    private static JObject RemoveEmptyFromObject(JObject obj)
    {
        JObject result = [];
        foreach (JProperty property in obj.Properties())
        {
            JToken child = property.Value.HasValues
                ? RemoveEmptyChildren(property.Value)
                : property.Value;
            if (!IsEmpty(child))
                result.Add(property.Name, child);
        }
        return result;
    }

    /// <summary>
    /// Removes empty children from a JSON array. If the array item has values (i.e. is an object or array itself),
    /// it is recursively processed to remove empty children. If the array item is empty, it is not included in the result.
    /// </summary>
    /// <param name="array">The JSON array to remove empty children from.</param>
    /// <returns>The JSON array with empty children removed.</returns>
    private static JArray RemoveEmptyFromArray(JArray array)
    {
        JArray result = [];

        foreach (JToken item in array)
        {
            JToken child = item.HasValues ? RemoveEmptyChildren(item) : item;
            if (!IsEmpty(child))
                result.Add(child);
        }
        return result;
    }

    /// <summary>
    /// Checks whether the given JSON token is empty, i.e. has no values.
    /// </summary>
    /// <param name="token">The JSON token to check.</param>
    /// <returns><c>true</c> if the token is empty; otherwise, <c>false</c>.</returns>
    /// <remarks>
    /// An empty token is defined as follows:
    /// <ul>
    /// <li>The token is null</li>
    /// <li>The token is a null JSON value</li>
    /// <li>The token is an empty JSON object (i.e. an object with no properties)</li>
    /// <li>The token is an empty JSON array (i.e. an array with no elements)</li>
    /// </ul>
    /// </remarks>
    internal static bool IsEmpty(JToken token)
    {
        return token == null
            || token.Type == JTokenType.Null
            || (token.Type == JTokenType.Object && !token.HasValues)
            || (token.Type == JTokenType.Array && !token.HasValues);
    }
}
