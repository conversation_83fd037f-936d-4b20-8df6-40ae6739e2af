﻿using System.Text.RegularExpressions;
using IDX.Utilities;
using Newtonsoft.Json.Linq;

namespace idc.decision_cb.Utilities.Extensions;

public static partial class StringExtensions
{
    /// <summary>
    /// Gets the language value corresponding to the given input string from the language token.
    /// If the language value is not found, returns a message indicating the missing language token.
    /// </summary>
    /// <param name="text">The input string to parse.</param>
    /// <param name="langToken">The language token used for parsing.</param>
    /// <returns>The parsed language token value from the input string, or a message indicating the missing language token.</returns>
    internal static string GetLanguageValue(this string? text, JToken? langToken)
    {
        if (string.IsNullOrEmpty(text))
            return string.Empty;
        if (langToken is null || !langToken.Any())
            return text;

        return langToken.GetValue<string>(text)
            ?? $"Can not find language with property token \"{text}\".";
    }

    /// <summary>
    /// Checks if the given string is a valid variable name.
    /// A valid variable name must start with a letter or underscore, and
    /// can only contain letters, numbers, and underscores.
    /// </summary>
    /// <param name="varName">The string to check.</param>
    /// <returns>True if the given string is a valid variable name, false otherwise.</returns>
    internal static bool IsValidVariableName(this string? varName)
    {
        if (string.IsNullOrWhiteSpace(varName))
            return false;
        return IsValidVariableName().IsMatch(varName);
    }

    /// <summary>
    /// Converts a query operator name to its corresponding query operator.
    /// </summary>
    /// <param name="name">The query operator name to convert.</param>
    /// <returns>The query operator corresponding to the given name.</returns>
    /// <example>
    /// <code>
    /// string queryOperatorName = "equal";
    /// string queryOperator = queryOperatorName.NameToQueryOperator();
    /// // queryOperator is now "="
    /// </code>
    /// </example>
    internal static string NameToQueryOperator(this string name) =>
        name.ToLower() switch
        {
            "equal" => "=",
            "equal to" => "=",
            "not equal" => "!=",
            "not equal to" => "!=",
            "not equals" => "!=",
            "not equals to" => "!=",
            "greater than" => ">",
            "greater than or equal" => ">=",
            "greater than or equal to" => ">=",
            "greater than or equals" => ">=",
            "greater than or equals to" => ">=",
            "less than" => "<",
            "less than or equal" => "<=",
            "less than or equal to" => "<=",
            "less than or equals" => "<=",
            "less than or equals to" => "<=",
            "in" => "IN",
            "not in" => "NOT IN",
            "like" => "LIKE",
            "not like" => "NOT LIKE",
            "between" => "BETWEEN",
            "not between" => "NOT BETWEEN",
            "is null" => "IS NULL",
            "is not null" => "IS NOT NULL",
            _ => name
        };

    /// <summary>
    /// Parses a language token from the input string, with replacements.
    /// </summary>
    /// <param name="text">The input string to parse.</param>
    /// <param name="langToken">The language token used for parsing.</param>
    /// <param name="replacements">An array of strings to replace in the parsed language token value.</param>
    /// <returns>The parsed language token value from the input string, with replacements.</returns>
    internal static string ParseLanguage(
        this string? text,
        JToken? langToken,
        string[] replacements
    )
    {
        string result = text.ParseLanguageToken(langToken);
        for (int i = 0; i < replacements.Length; i++)
        {
            result = result.Replace($"{{{i}}}", replacements[i]);
        }
        return result;
    }

    /// <summary>
    /// Parses a language token from the input string.
    /// </summary>
    /// <param name="text">The input string to parse.</param>
    /// <param name="langToken">The language token used for parsing.</param>
    /// <returns>The parsed language token value from the input string.</returns>
    internal static string ParseLanguageToken(this string? text, JToken? langToken)
    {
        if (string.IsNullOrWhiteSpace(text))
            return string.Empty;
        if (langToken is null || !langToken.Any())
            return text;

        return
            text.Contains("langToken:", StringComparison.CurrentCultureIgnoreCase)
            && text[..10].Equals("langToken:", StringComparison.CurrentCultureIgnoreCase)
            ? langToken[text[10..]]?.ToString() ?? text
            : langToken[text]?.ToString() ?? text;
    }

    /// <summary>
    /// Parses a language token from the input string, with optional replacements.
    /// </summary>
    /// <param name="text">The input string to parse.</param>
    /// <param name="langToken">The language token used for parsing.</param>
    /// <param name="replacement">The optional dictionary of replacements.</param>
    /// <returns>The parsed language token value from the input string, with replacements.</returns>
    internal static string ParseLanguageToken(
        this string? text,
        JToken? langToken,
        Dictionary<string, object>? replacement
    )
    {
        string result = text.ParseLanguageToken(langToken);
        return replacement?.Count == 0 ? result : result.BindWith(replacement!);
    }

    /// <summary>
    /// Converts a query operator to its corresponding name.
    /// </summary>
    /// <param name="queryOperator">The query operator to convert.</param>
    /// <returns>The name corresponding to the given query operator.</returns>
    /// <example>
    /// <code>
    /// string queryOperator = "=";
    /// string name = queryOperator.QueryOperatorToName();
    /// // name is now "equal"
    /// </code>
    /// </example>
    internal static string QueryOperatorToName(this string queryOperator) =>
        queryOperator.ToLower() switch
        {
            "=" => "equal",
            "!=" => "not equal",
            ">" => "greater than",
            ">=" => "greater than or equal",
            "<" => "less than",
            "<=" => "less than or equal",
            "IN" => "in",
            "NOT IN" => "not in",
            "LIKE" => "like",
            "NOT LIKE" => "not like",
            "BETWEEN" => "between",
            "NOT BETWEEN" => "not between",
            "IS NULL" => "is null",
            "IS NOT NULL" => "is not null",
            _ => queryOperator
        };

    /// <summary>
    /// Cleans the SqlKata query to make it valid SQL syntax.
    /// </summary>
    /// <param name="sqlKataQuery">The SqlKata query to clean.</param>
    /// <returns>The cleaned SqlKata query in valid SQL syntax.</returns>
    /// <example>
    /// <code>
    /// string sqlKataQuery = "SELECT [table_name].[column_name] AS column_alias FROM [table_name]";
    /// string cleanedQuery = sqlKataQuery.SqlKataQueryCleanser();
    /// // cleanedQuery is now "SELECT table_name.column_name AS column_alias FROM table_name"
    /// </code>
    /// </example>
    internal static string SqlKataQueryCleanser(this string? sqlKataQuery) =>
        string.IsNullOrWhiteSpace(sqlKataQuery)
            ? string.Empty
            : SqlKataCleanser().Replace(sqlKataQuery, "");

    /// <summary>
    /// Cleans the SqlKata query to make it valid SQL syntax with optional replacement.
    /// </summary>
    /// <param name="sqlKataQuery">The SqlKata query to clean.</param>
    /// <param name="replacement">The optional dictionary of replacements.</param>
    /// <returns>The cleaned SqlKata query in valid SQL syntax with replacements.</returns>
    /// <example>
    /// <code>
    /// string sqlKataQuery = "SELECT [table_name].[column_name] AS column_alias FROM [table_name]";
    /// Dictionary&lt;string, object&gt; replacements = new Dictionary&lt;string, object&gt; { { "table_name", "my_table" } };
    /// string cleanedQuery = sqlKataQuery.SqlKataQueryCleanser(replacements);
    /// // cleanedQuery is now "SELECT my_table.column_name AS column_alias FROM my_table"
    /// </code>
    /// </example>
    internal static string SqlKataQueryCleanser(
        this string? sqlKataQuery,
        Dictionary<string, object> replacement
    ) =>
        string.IsNullOrWhiteSpace(sqlKataQuery)
            ? string.Empty
            : SqlKataCleanser().Replace(sqlKataQuery.BindWith(replacement), "");

    /// <summary>
    /// Converts a JSON string to a <see cref="JObject"/>.
    /// </summary>
    /// <param name="value">The JSON string to convert.</param>
    /// <returns>A <see cref="JObject"/> representing the JSON string.</returns>
    /// <example>
    /// <code>
    /// string jsonString = "{\"key\":\"value\"}";
    /// JObject jObject = jsonString.ToJObject();
    /// // jObject now contains a JObject with a key "key" and value "value"
    /// </code>
    /// </example>
    internal static JObject ToJObject(this string? value) =>
        JObject.Parse(!string.IsNullOrWhiteSpace(value) ? value : "{}");

    /// <summary>
    /// Converts a JSON string to a <see cref="JArray"/>.
    /// </summary>
    /// <param name="value">The JSON string to convert.</param>
    /// <returns>A <see cref="JArray"/> representing the JSON string.</returns>
    /// <example>
    /// <code>
    /// string jsonString = "[\"value1\",\"value2\"]";
    /// JArray jArray = jsonString.ToJArray();
    /// // jArray now contains a JArray with values ["value1","value2"]
    /// </code>
    /// </example>
    internal static JArray ToJArray(this string? value) =>
        JArray.Parse(!string.IsNullOrWhiteSpace(value) ? value : "[]");

    /// <summary>
    /// A regular expression used to validate variable names.
    /// </summary>
    /// <remarks>
    /// A valid variable name must start with a letter or underscore, and can only contain letters, numbers, and underscores.
    /// </remarks>
    /// <example>
    /// <code>
    /// string variableName = "my_variable";
    /// bool isValid = IsValidVariableName().IsMatch(variableName);
    /// // isValid is now true
    /// </code>
    /// </example>
    [GeneratedRegex(@"^[a-zA-Z_][a-zA-Z0-9_]*$")]
    private static partial Regex IsValidVariableName();

    /// <summary>
    /// A regular expression used to remove SqlKata syntax from a query.
    /// </summary>
    /// <remarks>
    /// This regular expression is used to remove all SqlKata syntax from a query, such as "[table_name]" or "[column_name]".
    /// </remarks>
    /// <example>
    /// <code>
    /// string query = "SELECT [table_name].[column_name] FROM [table_name]";
    /// string cleanQuery = query.SqlKataQueryCleanser();
    /// // cleanQuery is now "SELECT table_name.column_name FROM table_name"
    /// </code>
    /// </example>
    [GeneratedRegex(@"\[(?!\[)[^]]*[a-zA-Z][^]]*\](?!\])")]
    private static partial Regex SqlKataCleanser();
}
