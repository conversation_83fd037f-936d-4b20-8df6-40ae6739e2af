#!/bin/bash

export TERM=xterm

source /apps/gitlab-runner/builds/mHDMZetj/0/idecision_source_net8/idc.decision_cb/Deployment
export $(cut -d= -f1 /apps/gitlab-runner/builds/mHDMZetj/0/idecision_source_net8/idc.decision_cb/Deployment)

# Check Kubernetes Cluster dev  
CNODE=$(kubectl --kubeconfig /home/<USER>/.kube/cluster-node-idecision-development.yaml get node | awk '{print $2}' | grep -v "STATUS" | wc -l)

if [ "2" = "$CNODE" ]; then
    # Check if the current pods are running the "latest" image
    CURRENT_TAG=$(kubectl --kubeconfig /home/<USER>/.kube/cluster-node-idecision-development.yaml --namespace dev get pods -l app=idc-decision-cb-dep -o=jsonpath='{.items[0].spec.containers[0].image}' | cut -d ':' -f 3)
    IDCNAME=$(kubectl --kubeconfig /home/<USER>/.kube/cluster-node-idecision-development.yaml --namespace dev get pods -l app=idc-decision-cb-dep -o=jsonpath='{.items[0].spec.containers[0].image}' | cut -d '-' -f 1 | sed 's/\// /g' | awk '{print $3}')

    if [ "$CURRENT_TAG" != "latest" ] || [ "$IDCNAME" != "idecision8" ]; then
        # Deploy new image to dev using kubectl set image
        kubectl --kubeconfig /home/<USER>/.kube/cluster-node-idecision-development.yaml --namespace dev set image deployment/idc-decision-cb-dep idc-decision-cb-dep="$DOCKERNIMAGESLS"
        echo "`date` || Deploy service update success $check" >> /apps/script/activity_deploy.log
    else
       # Deploy new image to dev using kubectl rollout restart
    kubectl --kubeconfig /home/<USER>/.kube/cluster-node-idecision-development.yaml --namespace dev rollout restart deployment/idc-decision-cb-dep
        echo "`date` || Current pods are already running the latest image, no update needed" >> /apps/script/activity_deploy.log
    fi
else

    # Check if the rollout was successful
    ROLLOUT_STATUS=$?
    if [ $ROLLOUT_STATUS -eq 0 ]; then
        echo "`date` || Deploy service update success $check" >> /apps/script/activity_deploy.log
    else
        echo "`date` || Deploy service update failed $check" >> /apps/script/activity_deploy.log
    fi
    sleep 660
fi
