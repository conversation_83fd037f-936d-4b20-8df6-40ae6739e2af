using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;

namespace idc.decision_cb.Utilities.Models.DecisionFlow.Output;

/// <summary>
/// Represents the output model for a decision flow configuration.
/// </summary>
public class DecisionFlowConfigurationOutputModel
{
    /// <summary>
    /// Gets or sets the decision flow code.
    /// </summary>
    [JsonProperty("df_code")]
    [Required(ErrorMessage = "DfConfigurationCbModel: df_code is required.")]
    public required string DFCode { get; set; }

    /// <summary>
    /// Gets or sets the decision flow name.
    /// </summary>
    [JsonProperty("df_name")]
    [Required(ErrorMessage = "DfConfigurationCbModel: df_name is required.")]
    public required string DFName { get; set; }

    /// <summary>
    /// Gets or sets the list of decision flow processes.
    /// </summary>
    [JsonProperty("df_process")]
    [Required(ErrorMessage = "DfConfigurationCbModel: df_process is required.")]
    public List<DecisionFlowProcess>? DFProcess { get; set; }

    /// <summary>
    /// Gets or sets the list of decision flow steps.
    /// </summary>
    [JsonProperty("df_steps")]
    [Required(ErrorMessage = "DfConfigurationCbModel: df_steps is required.")]
    public required List<DecisionFlowStep>? DFSteps { get; set; }

    /// <summary>
    /// Gets or sets the decision flow version.
    /// </summary>
    [JsonProperty("df_version")]
    [Required(ErrorMessage = "DfConfigurationCbModel: df_version is required.")]
    public required string DFVersion { get; set; }
}
