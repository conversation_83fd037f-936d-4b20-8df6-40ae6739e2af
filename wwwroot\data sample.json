{"tables": [{"joinType": null, "name": "table1", "alias": "table pertama", "mergeField": "field1"}, {"joinType": "inner", "name": "table2", "alias": "table kedua", "mergeField": "field1"}], "fields": [{"name": "field1", "alias": "field satu", "dataType": "<PERSON><PERSON><PERSON>", "length": 10}, {"name": "field2", "alias": "field kedua", "dataType": "bool", "length": 10}], "conditions": [{"multipleCondition": null, "field": "field1", "operation": "between", "value1": "10", "isFreeText1": true, "value2": "table2.field2", "isFreeText2": "false"}, {"multipleCondition": "AND", "field": "field1", "operation": "between", "value1": "10", "isFreeText1": true, "value2": "table2.field2", "isFreeText2": "false"}]}