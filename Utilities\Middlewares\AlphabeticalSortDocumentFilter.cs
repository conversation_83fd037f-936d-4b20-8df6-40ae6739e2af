﻿using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;

namespace idc.decision_cb.Utilities.Middlewares;

/// <summary>
/// Class to apply alphabetical sorting to the OpenAPI document paths.
/// </summary>
public class AlphabeticalSortDocumentFilter : IDocumentFilter
{
    /// <summary>
    /// Apply alphabetical sorting to the OpenAPI document paths.
    /// </summary>
    /// <param name="swaggerDoc">The OpenAPI document to apply sorting to.</param>
    /// <param name="context">The context of the document filter.</param>
    /// <exception cref="ArgumentNullException">Thrown when <paramref name="swaggerDoc"/> or <paramref name="context"/> is null.</exception>
    public void Apply(OpenApiDocument swaggerDoc, DocumentFilterContext context)
    {
        List<KeyValuePair<string, OpenApiPathItem>> sortedPaths =
        [
            .. swaggerDoc.Paths.OrderBy(entry => entry.Key)
        ];
        swaggerDoc.Paths.Clear();
        sortedPaths.ForEach(path => swaggerDoc.Paths.Add(path.Key, path.Value));
    }
}
